package com.github.cret.web.oee.calculator;

import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 产品模型计算器
 */
public class ProductModelCalculator {

	/**
	 * 获取指定时间段，对应设备最新的产品名称
	 * @param device 设备信息
	 * @param query 时间范围查询条件
	 * @param mongoTemplate MongoDB操作模板
	 * @return
	 */
	public static String getFirstProductModel(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 参数校验
		if (device == null || device.getCode() == null || device.getCode().trim().isEmpty()) {
			return null;
		}

		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		Aggregation aggregation = Aggregation.newAggregation(
				// 匹配时间范围
				Aggregation.match(Criteria.where("time").gte(query.getStartTime()).lte(query.getEndTime())),
				// 按时间降序排序
				Aggregation.sort(Sort.Direction.DESC, "time"),
				// 限制返回一条
				Aggregation.limit(1),
				// 只投影 productModel 字段
				Aggregation.project("productmodel"));

		// 执行聚合查询
		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		Document result = results.getUniqueMappedResult();
		return result != null ? result.getString("productmodel") : null;
	}

}
