package com.github.cret.web.security.spring.config;

import com.github.cret.web.security.config.SecurityProperties;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.filter.JwtFilter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.AnonymousConfigurer;
import org.springframework.security.config.annotation.web.configurers.LogoutConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.util.HashSet;

@Configuration
@EnableConfigurationProperties(SecurityProperties.class)
@EnableMethodSecurity
public class SecurityConfig {

	private final SecurityProperties properties;

	private final JwtFilter jwtFilter;

	public SecurityConfig(SecurityProperties securityProperties, JwtFilter jwtFilter) {
		this.jwtFilter = jwtFilter;
		this.properties = securityProperties;

	}

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {

		http.csrf(AbstractHttpConfigurer::disable)
			.httpBasic(AbstractHttpConfigurer::disable)
			.formLogin(AbstractHttpConfigurer::disable)
			.anonymous(this::configAnonymous)
			.exceptionHandling(e -> e.authenticationEntryPoint(authenticationEntrypoint()))
			.logout(LogoutConfigurer::disable)
			.authorizeHttpRequests(req -> req
				.requestMatchers(properties.getWhiteList()
					.stream()
					.map(AntPathRequestMatcher::antMatcher)
					.toArray(AntPathRequestMatcher[]::new))
				.permitAll()
				.anyRequest()
				.authenticated())
			.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
			.addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class);
		return http.build();
	}

	@Bean
	public CustomAuthenticationEntrypoint authenticationEntrypoint() {
		return new CustomAuthenticationEntrypoint();
	}

	public void configAnonymous(AnonymousConfigurer<HttpSecurity> configure) {
		AuthUser authUser = new AuthUser("-1", "anonymous", "*", "匿名", new HashSet<>(), "", "-1", "匿名部门", false, false);
		configure.principal(authUser).authorities("anonymous");
	}

}
