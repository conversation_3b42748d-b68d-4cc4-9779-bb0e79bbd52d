package com.github.cret.web.oee.service;

import java.util.List;
import java.util.Map;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.DeviceCtInfo;
import com.github.cret.web.oee.domain.analyze.DeviceDefectType;
import com.github.cret.web.oee.domain.analyze.DeviceProductModel;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.ProductionLineInfo;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;

/**
 * 基础分析类查询
 */
public interface BaseAnalyzeService {

	/**
	 * 获取单个设备的生产信息
	 * @param deviceQuery
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	List<ProductionData> getDeviceProductionData(AnalyzeQuery query);

	/**
	 * 获取每条轨道的产品信息
	 * @param query 查询条件
	 * @param primarySmtDevices 首序设备
	 * @return
	 */
	List<ProductionLineInfo> getCurrentProduction(AnalyzeQuery query, Map<String, Device> primarySmtDevices);

	/**
	 * 获取线体生产信息
	 * @param query 查询条件
	 * @param devices 设备
	 * @return
	 */
	Map<String, List<ProductionData>> getProductData(AnalyzeQuery query, List<Device> devices);

	/**
	 * 获取换线次数和换线时间
	 * @param query
	 * @param primarySmtDevices
	 * @return
	 */
	LineChangeoverInfo getLineChangeoverInfo(AnalyzeQuery query, Map<String, Device> primarySmtDevices);

	/**
	 * 计算良品率
	 * @param query 查询条件
	 * @param defectCounts 不良品数
	 * @param productData 生产数据
	 * @return
	 */
	Double calculateQuality(AnalyzeQuery query, Integer defectCounts, Map<String, List<ProductionData>> productData);

	/**
	 * 获取线体设备CT
	 * @param query
	 * @return
	 */
	List<DeviceCtInfo> getDevicesCt(AnalyzeQuery query);

	/**
	 * 收集每个设备及其对应当前产品型号
	 * @param devices
	 * @param query
	 * @return
	 */
	List<DeviceProductModel> collectDeviceProductModels(List<Device> devices, AnalyzeQuery query);

	/**
	 * 计算不良类型
	 * @param query
	 * @param devices
	 * @return
	 */
	List<DeviceDefectType> getDefectTypes(AnalyzeQuery query, List<Device> devices);

	/**
	 * 计算不良数量
	 * @param defectTypes
	 * @return
	 */
	Integer getDefectTotal(List<DeviceDefectType> defectTypes);

	/**
	 * 计算不良品数量
	 * @param query
	 * @return
	 */
	Integer calculateDefectCount(AnalyzeQuery query);

}
