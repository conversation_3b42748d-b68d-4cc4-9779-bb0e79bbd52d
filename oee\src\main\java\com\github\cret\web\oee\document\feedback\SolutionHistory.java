package com.github.cret.web.oee.document.feedback;

import java.util.Date;

import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 解决方案历史记录
 */
public class SolutionHistory {

	// 解决方案内容
	@Field(name = "solution")
	private String solution;

	// 解决人ID
	@Field(name = "solver_id")
	private String solverId;

	// 解决人姓名
	@Field(name = "solver_name")
	private String solverName;

	// 解决时间
	@Field(name = "solve_time")
	private Date solveTime;

	// 提交时间
	@Field(name = "submit_time")
	private Date submitTime;

	// 退回时间
	@Field(name = "reject_time")
	private Date rejectTime;

	// 退回原因
	@Field(name = "reject_reason")
	private String rejectReason;

	// 退回人ID
	@Field(name = "rejector_id")
	private String rejectorId;

	// 退回人姓名
	@Field(name = "rejector_name")
	private String rejectorName;

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getSolverId() {
		return solverId;
	}

	public void setSolverId(String solverId) {
		this.solverId = solverId;
	}

	public String getSolverName() {
		return solverName;
	}

	public void setSolverName(String solverName) {
		this.solverName = solverName;
	}

	public Date getSolveTime() {
		return solveTime;
	}

	public void setSolveTime(Date solveTime) {
		this.solveTime = solveTime;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public Date getRejectTime() {
		return rejectTime;
	}

	public void setRejectTime(Date rejectTime) {
		this.rejectTime = rejectTime;
	}

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	public String getRejectorId() {
		return rejectorId;
	}

	public void setRejectorId(String rejectorId) {
		this.rejectorId = rejectorId;
	}

	public String getRejectorName() {
		return rejectorName;
	}

	public void setRejectorName(String rejectorName) {
		this.rejectorName = rejectorName;
	}

}