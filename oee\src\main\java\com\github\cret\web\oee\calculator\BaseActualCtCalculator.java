package com.github.cret.web.oee.calculator;

import java.util.Comparator;
import java.util.List;

import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;

/**
 * 设备ct计算器
 */
public class BaseActualCtCalculator {

	/**
	 * 获取设备实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getDeviceCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		return switch (device.getType()) {
			case print_gkg -> getGkgPrintCt(device, query, mongoTemplate);
			case smt_npm_reporter -> getNpmSmtCt(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpmSmtCt(device, query, mongoTemplate);
			case smt_samsung -> getSamsungSmtCt(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaSmtCt(device, query, mongoTemplate);
			case aoi_yamaha -> getYamahaAoiCt(device, query, mongoTemplate);
			case aoi_viscom -> getViscomAoiCt(device, query, mongoTemplate);
			case aoi_jutze -> getJutzeAoiCt(device, query, mongoTemplate);
			case aoi_delu -> getDeluAoiCt(device, query, mongoTemplate);
			default -> 0.00;
		};
	}

	/**
	 * 获取印刷机实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getGkgPrintCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		return null;
	}

	/**
	 * 获取松下贴片机实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getNpmSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

	/**
	 * 获取三星贴片机实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getSamsungSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

	/**
	 * 获取雅马哈贴片机实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getYamahaSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

	/**
	 * 获取雅马哈AOI实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getYamahaAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		return null;
	}

	/**
	 * 获取viscomAOI实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getViscomAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

	/**
	 * 获取矩子AOI实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getJutzeAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

	/**
	 * 获取德律AOI实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getDeluAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);
		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);
		return result;
	}

}
