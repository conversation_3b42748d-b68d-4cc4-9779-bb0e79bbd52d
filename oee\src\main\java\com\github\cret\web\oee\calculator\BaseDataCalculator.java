package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.CollectionUtils;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.OeeUtil;
import com.github.cret.web.oee.utils.RedisUtil;

/**
 * 基础数据计算器
 */
public class BaseDataCalculator {

	/**
	 * 获取生产信息
	 * @param deviceQuery
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getDeviceProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 构建Redis缓存key
		String redisKey = device.getCode() + ":" + query.getStartTime().getTime() + ":" + query.getEndTime().getTime();

		// 尝试从缓存获取数据
		@SuppressWarnings("unchecked")
		List<ProductionData> cachedData = (List<ProductionData>) RedisUtil.getValue(redisKey);
		if (cachedData != null) {
			return cachedData;
		}

		// 缓存未命中，执行查询
		List<ProductionData> productionDataList = switch (device.getType()) {
			case smt_npm_reporter -> getNpmSmtProductionData(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpm2SmtProductionData(device, query, mongoTemplate);
			case smt_samsung -> getSamsungSmtProductionData(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaSmtProductionData(device, query, mongoTemplate);
			case aoi_yamaha -> getYamahaAoiProductionData(device, query, mongoTemplate);
			case aoi_viscom -> getViscomAoiProductionData(device, query, mongoTemplate);
			case aoi_jutze -> getJutzeAoiProductionData(device, query, mongoTemplate);
			case aoi_delu -> getDeluAoiProductionData(device, query, mongoTemplate);
			default -> Collections.emptyList();
		};

		if (CollectionUtils.isEmpty(productionDataList)) {
			return Collections.emptyList();
		}

		// 将结果存入缓存
		// RedisUtil.setValue(redisKey, productionDataList, 5, TimeUnit.MINUTES);
		return productionDataList;
	}

	/**
	 * 获取生产信息，将产品名称相同的数据进行汇总
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getDeviceProductionDataGroup(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产数据
		List<ProductionData> deviceProductionData = getDeviceProductionData(device, query, mongoTemplate);
		// 将产品名称相同的数据进行汇总
		Map<String, ProductionData> mergedMap = new HashMap<>();

		for (ProductionData productionData : deviceProductionData) {
			String originalProductModel = productionData.getOriginalProductModel();
			// 检查是否已存在该productModel的合并对象
			if (mergedMap.containsKey(originalProductModel)) {
				// 取出已存在的对象，累加属性
				ProductionData existing = mergedMap.get(originalProductModel);
				existing.setRunTime(existing.getRunTime() + productionData.getRunTime());
				existing.setStopTime(existing.getStopTime() + productionData.getStopTime());
				existing.setTotalTime(existing.getTotalTime() + productionData.getTotalTime());
				existing.setScStopTime(existing.getScStopTime() + productionData.getScStopTime());
				existing.setScEmergencyStopTime(
						existing.getScEmergencyStopTime() + productionData.getScEmergencyStopTime());
				existing.setModuleNum(existing.getModuleNum() + productionData.getModuleNum());
				existing.setBoardNum(existing.getBoardNum() + productionData.getBoardNum());
				existing.calculateActualCt();
			}
			else {
				// 创建新对象并存入Map
				ProductionData newProductionData = BuilderUtil.builder(ProductionData::new)
					.with(ProductionData::setProductModel, productionData.getProductModel())
					.with(ProductionData::setOriginalProductModel, productionData.getOriginalProductModel())
					.with(ProductionData::setGroupId, null)
					.with(ProductionData::setDeviceCode, productionData.getDeviceCode())
					.with(ProductionData::setDeviceCategory, productionData.getDeviceCategory())
					.with(ProductionData::setTrackType, productionData.getTrackType())
					.with(ProductionData::setRunTime, productionData.getRunTime())
					.with(ProductionData::setStopTime, productionData.getStopTime())
					.with(ProductionData::setTotalTime, productionData.getTotalTime())
					.with(ProductionData::setScStopTime, productionData.getScStopTime())
					.with(ProductionData::setScEmergencyStopTime, productionData.getScEmergencyStopTime())
					.with(ProductionData::setModuleNum, productionData.getModuleNum())
					.with(ProductionData::setBoardNum, productionData.getBoardNum())
					.with(ProductionData::setFlatNum, productionData.getFlatNum())
					.with(ProductionData::setTheoreticalCt, productionData.getTheoreticalCt())
					.with(ProductionData::setActualCt, productionData.getActualCt())
					// 设置瓶颈标志
					.with(ProductionData::setBottleneck, productionData.getBottleneck())
					.with(ProductionData::setDeviceGroup, productionData.getDeviceGroup())
					.build();
				mergedMap.put(originalProductModel, newProductionData);
			}
		}

		// 将Map的Value转换为List
		return new ArrayList<>(mergedMap.values());
	}

	/**
	 * 获取松下贴片机生产数据
	 * @param deviceCode
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getNpmSmtProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配并过滤产品型号为空的
		pipeline.add(
				new Document("$match",
						new Document()
							.append("time",
									new Document().append("$gte", query.getStartTime())
										.append("$lte", query.getEndTime()))
							.append("productmodel", new Document("$ne", ""))));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 获取各指标的前一个值
		Document previousValuesStage = new Document("$setWindowFields",
				new Document("sortBy",
						new Document("time", 1).append("totaltime", -1))
					.append("output", new Document()
						.append("previousActualTime",
								new Document("$shift", new Document("output", "$actualtime").append("by", -1)))
						.append("previousStopTime",
								new Document("$shift", new Document("output", "$stoptime").append("by", -1)))
						.append("previousTotalTime",
								new Document("$shift", new Document("output", "$totaltime").append("by", -1)))
						.append("previousBoard",
								new Document("$shift", new Document("output", "$raw.count.board").append("by", -1)))
						.append("previousModule",
								new Document("$shift", new Document("output", "$raw.count.module").append("by", -1)))
						.append("previousScstop",
								new Document("$shift", new Document("output", "$raw.time.scstop").append("by", -1)))
						.append("previousScestop",
								new Document("$shift", new Document("output", "$raw.time.scestop").append("by", -1)))));
		pipeline.add(previousValuesStage);

		// 6. 计算差值
		Document addFields = new Document("$addFields",
				new Document()
					.append("actualTimeResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$actualtime", "$previousActualTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$actualtime", "$previousActualTime")))
								.append("else", "$actualtime")))
					.append("stopTimeResult", new Document("$cond",
							new Document("if", new Document("$gte", Arrays.asList("$stoptime", "$previousStopTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$stoptime", "$previousStopTime")))
								.append("else", "$stoptime")))
					.append("totalTimeResult", new Document("$cond",
							new Document("if", new Document("$gte", Arrays.asList("$totaltime", "$previousTotalTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$totaltime", "$previousTotalTime")))
								.append("else", "$totaltime")))
					.append("boardResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.count.board", "$previousBoard")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.count.board", "$previousBoard")))
								.append("else", "$raw.count.board")))
					.append("moduleResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.count.module", "$previousModule")))
								.append("then",
										new Document("$subtract",
												Arrays.asList("$raw.count.module", "$previousModule")))
								.append("else", "$raw.count.module")))
					.append("scstopResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.time.scstop", "$previousScstop")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.time.scstop", "$previousScstop")))
								.append("else", "$raw.time.scstop")))
					.append("scestopResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.time.scestop", "$previousScestop")))
								.append("then",
										new Document("$subtract",
												Arrays.asList("$raw.time.scestop", "$previousScestop")))
								.append("else", "$raw.time.scestop"))));
		pipeline.add(addFields);

		// 7. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 8. 按productmodel和groupId分组
		Document groupBySegment = new Document("$group",
				new Document("_id", new Document("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("stopTimeTotal", new Document("$sum", "$stopTimeResult"))
					.append("totalTimeTotal", new Document("$sum", "$totalTimeResult"))
					.append("boardTotal", new Document("$sum", "$boardResult"))
					.append("moduleTotal", new Document("$sum", "$moduleResult"))
					.append("scstopTotal", new Document("$sum", "$scstopResult"))
					.append("scestopTotal", new Document("$sum", "$scestopResult")));
		pipeline.add(groupBySegment);

		// 9. 最终按productmodel汇总
		// Document finalGroup = new Document("$group",
		// new Document("_id", "$_id.productmodel")
		// .append("actualTimeTotal", new Document("$sum", "$actualTimeTotal"))
		// .append("stopTimeTotal", new Document("$sum", "$stopTimeTotal"))
		// .append("totalTimeTotal", new Document("$sum", "$totalTimeTotal"))
		// .append("boardTotal", new Document("$sum", "$boardTotal"))
		// .append("moduleTotal", new Document("$sum", "$moduleTotal"))
		// .append("scstopTotal", new Document("$sum", "$scstopTotal"))
		// .append("scestopTotal", new Document("$sum", "$scestopTotal")));
		// pipeline.add(finalGroup);

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");
			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(productModel);
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setStopTime(doc.getDouble("stopTimeTotal"));
			data.setTotalTime(doc.getDouble("totalTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setModuleNum(doc.getInteger("moduleTotal"));
			data.setScStopTime(doc.getDouble("scstopTotal"));
			data.setScEmergencyStopTime(doc.getDouble("scestopTotal"));
			// 设置分组
			data.setDeviceGroup(device.getGroup());
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取松下贴片机（双轨）生产数据
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getNpm2SmtProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配并过滤产品型号为空的
		pipeline.add(
				new Document("$match",
						new Document()
							.append("time",
									new Document().append("$gte", query.getStartTime())
										.append("$lte", query.getEndTime()))
							.append("productmodel", new Document("$ne", ""))));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 获取各指标的前一个值
		Document previousValuesStage = new Document("$setWindowFields",
				new Document("sortBy",
						new Document("time", 1).append("totaltime", -1))
					.append("output", new Document()
						.append("previousActualTime",
								new Document("$shift", new Document("output", "$actualtime").append("by", -1)))
						.append("previousStopTime",
								new Document("$shift", new Document("output", "$stoptime").append("by", -1)))
						.append("previousTotalTime",
								new Document("$shift", new Document("output", "$totaltime").append("by", -1)))
						.append("previousBoard",
								new Document("$shift", new Document("output", "$raw.count.board").append("by", -1)))
						.append("previousModule",
								new Document("$shift", new Document("output", "$raw.count.module").append("by", -1)))
						.append("previousScstop",
								new Document("$shift", new Document("output", "$raw.time.scstop").append("by", -1)))
						.append("previousScestop",
								new Document("$shift", new Document("output", "$raw.time.scestop").append("by", -1)))));
		pipeline.add(previousValuesStage);

		// 6. 计算差值
		Document addFields = new Document("$addFields",
				new Document()
					.append("actualTimeResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$actualtime", "$previousActualTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$actualtime", "$previousActualTime")))
								.append("else", "$actualtime")))
					.append("stopTimeResult", new Document("$cond",
							new Document("if", new Document("$gte", Arrays.asList("$stoptime", "$previousStopTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$stoptime", "$previousStopTime")))
								.append("else", "$stoptime")))
					.append("totalTimeResult", new Document("$cond",
							new Document("if", new Document("$gte", Arrays.asList("$totaltime", "$previousTotalTime")))
								.append("then",
										new Document("$subtract", Arrays.asList("$totaltime", "$previousTotalTime")))
								.append("else", "$totaltime")))
					.append("boardResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.count.board", "$previousBoard")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.count.board", "$previousBoard")))
								.append("else", "$raw.count.board")))
					.append("moduleResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.count.module", "$previousModule")))
								.append("then",
										new Document("$subtract",
												Arrays.asList("$raw.count.module", "$previousModule")))
								.append("else", "$raw.count.module")))
					.append("scstopResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.time.scstop", "$previousScstop")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.time.scstop", "$previousScstop")))
								.append("else", "$raw.time.scstop")))
					.append("scestopResult", new Document("$cond",
							new Document("if",
									new Document("$gte", Arrays.asList("$raw.time.scestop", "$previousScestop")))
								.append("then",
										new Document("$subtract",
												Arrays.asList("$raw.time.scestop", "$previousScestop")))
								.append("else", "$raw.time.scestop"))));
		pipeline.add(addFields);

		// 7. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 8. 按productmodel和groupId分组
		Document groupBySegment = new Document("$group",
				new Document("_id", new Document("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("stopTimeTotal", new Document("$sum", "$stopTimeResult"))
					.append("totalTimeTotal", new Document("$sum", "$totalTimeResult"))
					.append("boardTotal", new Document("$sum", "$boardResult"))
					.append("moduleTotal", new Document("$sum", "$moduleResult"))
					.append("scstopTotal", new Document("$sum", "$scstopResult"))
					.append("scestopTotal", new Document("$sum", "$scestopResult")));
		pipeline.add(groupBySegment);

		// 9. 最终按productmodel汇总
		// Document finalGroup = new Document("$group",
		// new Document("_id", "$_id.productmodel")
		// .append("actualTimeTotal", new Document("$sum", "$actualTimeTotal"))
		// .append("stopTimeTotal", new Document("$sum", "$stopTimeTotal"))
		// .append("totalTimeTotal", new Document("$sum", "$totalTimeTotal"))
		// .append("boardTotal", new Document("$sum", "$boardTotal"))
		// .append("moduleTotal", new Document("$sum", "$moduleTotal"))
		// .append("scstopTotal", new Document("$sum", "$scstopTotal"))
		// .append("scestopTotal", new Document("$sum", "$scestopTotal")));
		// pipeline.add(finalGroup);

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");

			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(removeTopOrBotAndAfter(productModel));
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setStopTime(doc.getDouble("stopTimeTotal"));
			data.setTotalTime(doc.getDouble("totalTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setModuleNum(doc.getInteger("moduleTotal"));
			data.setScStopTime(doc.getDouble("scstopTotal"));
			data.setScEmergencyStopTime(doc.getDouble("scestopTotal"));
			// 设置分组
			data.setDeviceGroup(device.getGroup());
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取三星贴片机生产数据
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getSamsungSmtProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(new Document("$match",
				new Document("time",
						new Document().append("$gte", query.getStartTime()).append("$lte", query.getEndTime()))
					.append("normalflag", new Document("$eq", true))));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 添加计算字段
		pipeline.add(new Document("$addFields",
				new Document()
					.append("runtime1",
							new Document("$add",
									Arrays.asList(new Document("$ifNull", Arrays.asList("$samsungindex.placetime", 0)),
											new Document("$ifNull", Arrays.asList("$samsungindex.transtime", 0)))))
					.append("stoptime1",
							new Document("$add",
									Arrays.asList(new Document("$ifNull", Arrays.asList("$samsungindex.waittime", 0)),
											new Document("$ifNull", Arrays.asList("$samsungindex.stoptime", 0)),
											new Document("$ifNull", Arrays.asList("$samsungindex.idletime", 0)))))
					.append("totaltime1",
							new Document("$add",
									Arrays.asList(new Document("$ifNull", Arrays.asList("$samsungindex.runtime", 0)),
											new Document("$ifNull", Arrays.asList("$samsungindex.stoptime", 0)),
											new Document("$ifNull", Arrays.asList("$samsungindex.idletime", 0)))))
					.append("moduleNum", new Document("$ifNull", Arrays.asList("$samsungindex.pcbcount", 0)))
					.append("boardNum", new Document("$ifNull", Arrays.asList("$samsungindex.panelcount", 0)))));

		// 6. 分组统计
		pipeline.add(new Document("$group",
				new Document("_id",
						new Document().append("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("runtime2", new Document("$sum", "$runtime1"))
					.append("stoptime2", new Document("$sum", "$stoptime1"))
					.append("totaltime2", new Document("$sum", "$totaltime1"))
					.append("totalModuleNum", new Document("$sum", "$moduleNum"))
					.append("totalBoardNum", new Document("$sum", "$boardNum"))));

		// 7. 最终投影
		pipeline
			.add(new Document("$project",
					new Document().append("_id", 1)
						.append("runtime2", 1)
						.append("stoptime2", 1)
						.append("totaltime2", 1)
						.append("moduleNum", "$totalModuleNum")
						.append("boardNum", "$totalBoardNum")
						.append("flatNum",
								new Document("$cond",
										new Document("if", new Document("$gt", Arrays.asList("$totalBoardNum", 0)))
											.append("then",
													new Document("$ceil", new Document("$divide",
															Arrays.asList("$totalModuleNum", "$totalBoardNum"))))
											.append("else", 0)))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");

			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(extractBase(productModel));
			// 设置产品分组ID
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(Double.valueOf(doc.getInteger("runtime2")));
			data.setStopTime(Double.valueOf(doc.getInteger("stoptime2")));
			data.setTotalTime(Double.valueOf(doc.getInteger("totaltime2")));
			data.setBoardNum(doc.getInteger("boardNum"));
			data.setModuleNum(doc.getInteger("moduleNum"));
			Object flatNumObj = doc.get("flatNum");
			if (flatNumObj instanceof Number) {
				data.setFlatNum(((Number) flatNumObj).intValue());
			}
			else {
				data.setFlatNum(0); // 或根据实际情况设为null或其他默认值
			}
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 设置设备分组
			data.setDeviceGroup(device.getGroup());
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取yamaha 贴片机生产数据
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getYamahaSmtProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(AggregationPipelineUtil.buildTimeRangeMatch(query.getStartTime(), query.getEndTime(), "time"));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 移除临时字段
		pipeline.add(new Document("$project", new Document("prevProduct", 0)));

		// 6. 计算聚合时间字段
		pipeline.add(
				new Document("$addFields",
						new Document()
							.append("addruntime",
									new Document("$add", Arrays.asList("$mountingcta", "$mountingctb", "$mountingctc",
											"$mountingctd", "$transferct", "$markrecognitioncta", "$markrecognitionctb",
											"$markrecognitionctc", "$markrecognitionctd")))
							.append("addstoptime",
									new Document("$add",
											Arrays.asList("$standbyct", "$errorshutdowntime", "$errorrecoverytime",
													"$awaitingothertracktime", "$operatorshutdowntime",
													"$otherconveyingtabletime")))));

		// 7. 第一次分组（按复合键）
		pipeline.add(new Document("$group",
				new Document()
					.append("_id",
							new Document().append("productmodel", "$productmodel")
								.append("groupId", "$groupId")
								.append("batchsequencenumber", "$batchsequencenumber"))
					.append("runtime", new Document("$avg", "$addruntime"))
					.append("stoptime", new Document("$avg", "$addstoptime"))
					.append("completedpanelcount", new Document("$last", "$completedpanelcount"))));

		// 8. 最终分组统计
		pipeline.add(new Document("$group",
				new Document()
					.append("_id",
							new Document().append("productmodel", "$_id.productmodel")
								.append("groupId", "$_id.groupId"))
					.append("actualTimeTotal", new Document("$sum", "$runtime"))
					.append("stopTimeTotal", new Document("$sum", "$stoptime"))
					.append("boardTotal", new Document("$sum", 1))
					.append("moduleTotal", new Document("$sum", "$completedpanelcount"))
					.append("flatnum", new Document("$first", "$completedpanelcount"))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");

			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(extractBase(productModel));
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setStopTime(doc.getDouble("stopTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setModuleNum(doc.getInteger("moduleTotal"));
			// 总时间为运行时间和停机时间之和
			data.setTotalTime(data.getRunTime() + data.getStopTime());
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取yamaha aoi 的生产数据
	 * @param deviceCode
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getYamahaAoiProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 依据轨道类型选择不同的生产信息
		switch (query.getLineType()) {
			case singleTrack:
				return getYamahaAoiProductionDataSingleTrack(device, query, mongoTemplate);
			case dualTrack:
				return getYamahaAoiProductionDataDualTrack(device, query, mongoTemplate);
			default:
				break;
		}
		return Collections.emptyList();
	}

	/**
	 * 获取yamaha生产数据单轨
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<ProductionData> getYamahaAoiProductionDataSingleTrack(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(AggregationPipelineUtil.buildTimeRangeMatch(query.getStartTime(), query.getEndTime(), "time"));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 获取各指标的前一个值
		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output", new Document("previousTime",
						new Document("$shift", new Document("output", "$time").append("by", -1))))));

		// 6. 计算差值
		pipeline
			.add(new Document("$addFields",
					new Document("actualTimeResult",
							new Document("$cond",
									new Document("if",
											new Document("$gte",
													Arrays.asList(new Document("$toLong", "$time"),
															new Document("$toLong", "$previousTime"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$time"),
																		new Document("$toLong", "$previousTime"))),
														1000)))
										.append("else", 0)))));

		// 7. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 8. 按productmodel和groupId分组
		pipeline.add(new Document("$group",
				new Document("_id", new Document("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("boardTotal", new Document("$sum", 1))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");

			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setProductModel(productModel);
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setStopTime(0.0);
			data.setTotalTime(0.0);
			data.setModuleNum(0);
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取yamha生产数据双轨
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<ProductionData> getYamahaAoiProductionDataDualTrack(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(new Document("$match", new Document("time",
				new Document().append("$gte", query.getStartTime()).append("$lte", query.getEndTime()))));

		// 2. 按轨道和时间排序
		pipeline.add(new Document("$sort", new Document("tracktype", 1).append("time", 1)));

		// 3. 按轨道分区获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", "$tracktype").append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 按轨道分区生成分组ID
		pipeline.add(new Document("$setWindowFields", new Document("partitionBy", "$tracktype")
			.append("sortBy", new Document("time", 1))
			.append("output", new Document("groupId", new Document("$sum",
					new Document("$cond",
							Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevProduct")), 1, 0)))
				.append("window", new Document("documents", Arrays.asList("unbounded", "current")))))));

		// 5. 获取前一个时间点
		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output", new Document("previousTime",
						new Document("$shift", new Document("output", "$time").append("by", -1))))));

		// 6. 计算时间差值
		pipeline
			.add(new Document("$addFields",
					new Document("actualTimeResult",
							new Document("$cond",
									new Document("if",
											new Document("$gte",
													Arrays.asList(new Document("$toLong", "$time"),
															new Document("$toLong", "$previousTime"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$time"),
																		new Document("$toLong", "$previousTime"))),
														1000)))
										.append("else", 0)))));

		// 7. 过滤无效记录并根据device的track进行过滤
		pipeline.add(new Document("$match",
				new Document("$and", Arrays.asList(new Document("actualTimeResult", new Document("$ne", null)),
						new Document("tracktype", device.getTrack())))));

		// 8. 按productmodel,groupId,tracktype分组统计
		pipeline.add(new Document("$group",
				new Document("_id",
						new Document("productmodel", "$productmodel").append("groupId", "$groupId")
							.append("tracktype", "$tracktype"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("boardTotal", new Document("$sum", 1))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");
			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(productModel);
			data.setTrackType(id.getString("tracktype"));
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			// 设置默认值
			data.setStopTime(0.0);
			data.setTotalTime(data.getRunTime());
			data.setModuleNum(0);
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取viscom aoi的生产数据
	 * @param deviceCode
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getViscomAoiProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(AggregationPipelineUtil.buildTimeRangeMatch(query.getStartTime(), query.getEndTime(), "time"));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 获取各指标的前一个值
		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output", new Document("previousTime",
						new Document("$shift", new Document("output", "$time").append("by", -1))))));

		// 6. 计算差值
		pipeline
			.add(new Document("$addFields",
					new Document("actualTimeResult",
							new Document("$cond",
									new Document("if",
											new Document("$gte",
													Arrays.asList(new Document("$toLong", "$time"),
															new Document("$toLong", "$previousTime"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$time"),
																		new Document("$toLong", "$previousTime"))),
														1000)))
										.append("else", 0)))));

		// 7. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 8. 按productmodel和groupId分组
		pipeline.add(new Document("$group",
				new Document("_id", new Document("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("boardTotal", new Document("$sum", 1))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");
			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(productModel);
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setStopTime(0.0);
			data.setTotalTime(0.0);
			data.setModuleNum(0);
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取jutze aoi的生产数据
	 * @param deviceCode
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<ProductionData> getJutzeAoiProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(AggregationPipelineUtil.buildTimeRangeMatch(query.getStartTime(), query.getEndTime(), "time"));

		// 2. 时间排序
		pipeline.add(AggregationPipelineUtil.buildSortStage("time", 1));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成变化分组ID
		pipeline.add(AggregationPipelineUtil.buildChangeGroupIdStage("productmodel", "prevProduct", "groupId",
				new Document("time", 1).append("totaltime", -1)));

		// 5. 获取各指标的前一个值
		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output", new Document("previousTime",
						new Document("$shift", new Document("output", "$time").append("by", -1))))));

		// 6. 计算差值
		pipeline
			.add(new Document("$addFields",
					new Document("actualTimeResult",
							new Document("$cond",
									new Document("if",
											new Document("$gte",
													Arrays.asList(new Document("$toLong", "$time"),
															new Document("$toLong", "$previousTime"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$time"),
																		new Document("$toLong", "$previousTime"))),
														1000)))
										.append("else", 0)))));

		// 7. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 8. 按productmodel和groupId分组
		pipeline.add(new Document("$group",
				new Document("_id", new Document("productmodel", "$productmodel").append("groupId", "$groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("boardTotal", new Document("$sum", 1))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");
			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(productModel);
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			data.setStopTime(0.0);
			data.setTotalTime(0.0);
			data.setModuleNum(0);
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 获取德律 aoi的生产数据
	 * @param deviceCode
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	/**
	 * 获取德律 aoi的生产数据
	 * @param device AOI设备
	 * @param query 查询参数
	 * @param mongoTemplate MongoDB模板
	 * @return 生产数据列表
	 */
	public static List<ProductionData> getDeluAoiProductionData(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 时间范围匹配
		pipeline.add(new Document("$match", new Document("time",
				new Document().append("$gte", query.getStartTime()).append("$lte", query.getEndTime()))));

		// 2. 时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 获取前一个产品型号
		pipeline.add(new Document("$setWindowFields",
				new Document("partitionBy", null).append("sortBy", new Document("time", 1))
					.append("output", new Document("prevProduct", new Document("$shift",
							new Document("output", "$productmodel").append("by", -1).append("default", null))))));

		// 4. 生成分组ID
		pipeline.add(new Document("$setWindowFields", new Document("partitionBy", null)
			.append("sortBy", new Document("time", 1))
			.append("output", new Document("groupId", new Document("$sum",
					new Document("$cond",
							Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevProduct")), 1, 0)))
				.append("window", new Document("documents", Arrays.asList("unbounded", "current")))))));

		// 5. 提取大板ID和保留必要字段
		pipeline.add(new Document("$project",
				new Document("groupId", 1).append("productmodel", 1)
					.append("mainBoardId", new Document("$substr", Arrays.asList("$serial", 0, 14)))
					.append("time", 1)));

		// 6. 按大板分组获取最早时间
		pipeline.add(new Document("$group",
				new Document("_id",
						new Document("groupId", "$groupId").append("productmodel", "$productmodel")
							.append("mainBoardId", "$mainBoardId"))
					.append("boardTime", new Document("$min", "$time"))));

		// 7. 计算时间差值
		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("boardTime", 1)).append("output", new Document("previousTime",
						new Document("$shift", new Document("output", "$boardTime").append("by", -1))))));

		// 8. 添加时间差值计算字段
		pipeline
			.add(new Document("$addFields",
					new Document("actualTimeResult",
							new Document("$cond",
									new Document("if",
											new Document("$gte",
													Arrays.asList(new Document("$toLong", "$boardTime"),
															new Document("$toLong", "$previousTime"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$boardTime"),
																		new Document("$toLong", "$previousTime"))),
														1000)))
										.append("else", 0)))));

		// 9. 过滤无效记录
		pipeline.add(new Document("$match", new Document("actualTimeResult", new Document("$ne", null))));

		// 10. 最终分组统计
		pipeline.add(new Document("$group",
				new Document("_id", new Document("productmodel", "$_id.productmodel").append("groupId", "$_id.groupId"))
					.append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
					.append("boardTotal", new Document("$sum", 1))));

		// 执行查询并转换结果
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<ProductionData> list = new ArrayList<>();
		for (Document doc : results) {
			Document id = (Document) doc.get("_id");
			String productModel = id.getString("productmodel");

			// 跳过productmodel为null或空字符串的记录
			if (productModel == null || productModel.trim().isEmpty()) {
				continue;
			}

			ProductionData data = new ProductionData();
			data.setDeviceCode(device.getCode());
			data.setDeviceCategory(device.getCategory());
			data.setOriginalProductModel(productModel);
			data.setProductModel(productModel);
			data.setGroupId(id.getInteger("groupId"));
			data.setRunTime(doc.getDouble("actualTimeTotal"));
			data.setBoardNum(doc.getInteger("boardTotal"));
			// 设置默认值
			data.setStopTime(0.0);
			data.setTotalTime(data.getRunTime());
			data.setModuleNum(0);
			data.setScStopTime(0.0);
			data.setScEmergencyStopTime(0.0);
			// 计算实际CT
			data.calculateActualCt();
			// 计算拼板数
			data.calculateFlatNumber();
			list.add(data);
		}

		return list;
	}

	/**
	 * 移除TOP或者BOT
	 */
	public static String removeTopOrBotAndAfter(String input) {
		// 找到"TOP"或"BOT"的位置
		int topIndex = input.indexOf("TOP");
		int botIndex = input.indexOf("BOT");

		// 确定是"TOP"还是"BOT"
		int targetIndex = -1;
		if (topIndex != -1) {
			targetIndex = topIndex;
		}
		else if (botIndex != -1) {
			targetIndex = botIndex;
		}

		// 如果找到"TOP"或"BOT"，则截取前面的部分
		if (targetIndex != -1) {
			return input.substring(0, targetIndex - 1); // 减去1以去除前面的"-"
		}

		// 如果没有找到"TOP"或"BOT"，返回原字符串
		return input;
	}

	private static String extractBase(String input) {
		int lastDashIndex = input.lastIndexOf('-');
		if (lastDashIndex != -1 && lastDashIndex < input.length() - 1) {
			String prefix = input.substring(0, lastDashIndex + 1);
			String suffix = input.substring(lastDashIndex + 1);
			String trimmedSuffix = trimTrailingDigits(suffix);
			return prefix + trimmedSuffix;
		}
		return input; // 格式不符时返回原字符串
	}

	private static String trimTrailingDigits(String s) {
		if (s.isEmpty()) {
			return s;
		}
		int i = s.length() - 1;
		while (i >= 0 && Character.isDigit(s.charAt(i))) {
			i--;
		}
		return s.substring(0, i + 1);
	}

}
