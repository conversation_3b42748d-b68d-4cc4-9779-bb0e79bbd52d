package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.analyze.Abnormal;
import com.github.cret.web.oee.repository.AbnormalRepository;

@Service
public class AbnormalCheckService {

	@Autowired
	private AbnormalRepository abnormalRepository;

	// 每 10 分钟执行一次
	@Scheduled(fixedRate = 600000)
	public void checkAndUpdateRecords() {
		Date now = new Date();
		Date twelveHoursAgo = new Date(now.getTime() - 12 * 60 * 60 * 1000);

		// 查找所有 writeTime 超过 12 小时且 confirmtime 为空的记录
		List<Abnormal> records = abnormalRepository.findByWriteTimeBeforeAndConfirmTimeIsNull(twelveHoursAgo);

		for (Abnormal record : records) {
			// 更新记录
			record.setConfirmTime(now);
			record.setConfirm("管理员");
			abnormalRepository.save(record);
		}
	}

}