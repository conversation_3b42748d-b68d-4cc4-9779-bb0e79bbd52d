package com.github.cret.web.oee.domain.analyze;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

@ExcelIgnoreUnannotated
public class OeeResult {

	/**
	 * 计划时间
	 */
	// @ExcelProperty(value = "计划时间")
	private String planTime;

	/**
	 * 实际计划时间
	 */
	@ExcelProperty(value = "开班时间(h)")
	private String actualPlanTime;

	/**
	 * 运行时间
	 */
	@ExcelProperty(value = "运机时间(h)")
	private String runTime;

	/**
	 * 停止时间
	 */
	@ExcelProperty(value = "停机时间(h)")
	private String stopTime;

	/**
	 * 实际停止时间
	 */
	// @ExcelProperty(value = "实际停止时间")
	private String actualStopTime;

	/**
	 * 换线次数
	 */
	@ExcelProperty(value = "换线次数")
	private String changeoverNum;

	/**
	 * 换线时间
	 */
	@ExcelProperty(value = "换线时间(h)")
	private String changeoverTime;

	/**
	 * 理论数量
	 */
	@ExcelProperty(value = "标准生产数(件)")
	private String planBoard;

	/**
	 * 生产数量
	 */
	@ExcelProperty(value = "实际生产数(件)")
	private String actualBoard;

	/**
	 * 计划产品点数
	 */
	@ExcelProperty(value = "标准产品点数")
	private String planBoardPoints;

	/**
	 * 实际产品点数
	 */
	@ExcelProperty(value = "实际产品点数")
	private String actualBoardPoints;

	/**
	 * 良品数量
	 */
	// @ExcelProperty(value = "良品数量")
	private String goodBoard;

	/**
	 * 稼动率
	 */
	@ExcelProperty(value = "实际运转率")
	private String availability;

	/**
	 * 稼动率目标
	 */
	private String availabilityTarget;

	/**
	 * 运行效率
	 */
	@ExcelProperty(value = "实际有效生产率")
	private String performance;

	/**
	 * 运行效率目标
	 */
	private String performanceTarget;

	/**
	 * 不良数量
	 */
	@ExcelProperty(value = "不良品数")
	private String defectCount;

	/**
	 * 良品率
	 */
	@ExcelProperty(value = "实际良品率")
	private String quality;

	/**
	 * 良品率目标
	 */
	private String qualityTarget;

	/**
	 * OEE值
	 */
	@ExcelProperty(value = "实际OEE")
	private String oee;

	/**
	 * OEE目标
	 */
	private String oeeTarget;

	public String getChangeoverNum() {
		return changeoverNum;
	}

	public void setChangeoverNum(String changeoverNum) {
		this.changeoverNum = changeoverNum;
	}

	public String getChangeoverTime() {
		return changeoverTime;
	}

	public void setChangeoverTime(String changeoverTime) {
		this.changeoverTime = changeoverTime;
	}

	public String getPlanTime() {
		return planTime;
	}

	public void setPlanTime(String planTime) {
		this.planTime = planTime;
	}

	public String getActualPlanTime() {
		return actualPlanTime;
	}

	public void setActualPlanTime(String actualPlanTime) {
		this.actualPlanTime = actualPlanTime;
	}

	public String getRunTime() {
		return runTime;
	}

	public void setRunTime(String runTime) {
		this.runTime = runTime;
	}

	public String getStopTime() {
		return stopTime;
	}

	public void setStopTime(String stopTime) {
		this.stopTime = stopTime;
	}

	public String getActualStopTime() {
		return actualStopTime;
	}

	public void setActualStopTime(String actualStopTime) {
		this.actualStopTime = actualStopTime;
	}

	public String getActualBoard() {
		return actualBoard;
	}

	public void setActualBoard(String actualBoard) {
		this.actualBoard = actualBoard;
	}

	public String getPlanBoard() {
		return planBoard;
	}

	public void setPlanBoard(String planBoard) {
		this.planBoard = planBoard;
	}

	public String getActualBoardPoints() {
		return actualBoardPoints;
	}

	public void setActualBoardPoints(String actualBoardPoints) {
		this.actualBoardPoints = actualBoardPoints;
	}

	public String getPlanBoardPoints() {
		return planBoardPoints;
	}

	public void setPlanBoardPoints(String planBoardPoints) {
		this.planBoardPoints = planBoardPoints;
	}

	public String getGoodBoard() {
		return goodBoard;
	}

	public void setGoodBoard(String goodBoard) {
		this.goodBoard = goodBoard;
	}

	public String getAvailability() {
		return availability;
	}

	public void setAvailability(String availability) {
		this.availability = availability;
	}

	public String getAvailabilityTarget() {
		return availabilityTarget;
	}

	public void setAvailabilityTarget(String availabilityTarget) {
		this.availabilityTarget = availabilityTarget;
	}

	public String getPerformance() {
		return performance;
	}

	public void setPerformance(String performance) {
		this.performance = performance;
	}

	public String getPerformanceTarget() {
		return performanceTarget;
	}

	public void setPerformanceTarget(String performanceTarget) {
		this.performanceTarget = performanceTarget;
	}

	public String getDefectCount() {
		return defectCount;
	}

	public void setDefectCount(String defectCount) {
		this.defectCount = defectCount;
	}

	public String getQuality() {
		return quality;
	}

	public void setQuality(String quality) {
		this.quality = quality;
	}

	public String getQualityTarget() {
		return qualityTarget;
	}

	public void setQualityTarget(String qualityTarget) {
		this.qualityTarget = qualityTarget;
	}

	public String getOee() {
		return oee;
	}

	public void setOee(String oee) {
		this.oee = oee;
	}

	public String getOeeTarget() {
		return oeeTarget;
	}

	public void setOeeTarget(String oeeTarget) {
		this.oeeTarget = oeeTarget;
	}

}