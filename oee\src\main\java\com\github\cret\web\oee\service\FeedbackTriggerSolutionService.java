package com.github.cret.web.oee.service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.domain.query.FeedbackTriggerSolutionQuery;

import java.util.List;

public interface FeedbackTriggerSolutionService {

	/**
	 * 保存解决方案
	 * @param solution 解决方案
	 * @return 保存后的解决方案
	 */
	FeedbackTriggerSolution save(FeedbackTriggerSolution solution);

	/**
	 * 删除解决方案
	 * @param id 解决方案ID
	 */
	void delete(String id);

	/**
	 * 根据ID查找解决方案
	 * @param id 解决方案ID
	 * @return 解决方案
	 */
	FeedbackTriggerSolution findById(String id);

	/**
	 * 分页搜索解决方案
	 * @param query 查询条件
	 * @return 分页结果
	 */
	PageList<FeedbackTriggerSolution> search(PageableParam<FeedbackTriggerSolutionQuery> query);

	/**
	 * 更新解决方案
	 * @param id 解决方案ID
	 * @param solution 解决方案数据
	 * @return 更新后的解决方案
	 */
	FeedbackTriggerSolution update(String id, FeedbackTriggerSolution solution);

	/**
	 * 根据触发记录ID查找解决方案列表
	 * @param triggerRecordId 触发记录ID
	 * @return 解决方案列表
	 */
	List<FeedbackTriggerSolution> findByTriggerRecordId(String triggerRecordId);

	/**
	 * 提交解决方案
	 * @param id 解决方案ID
	 * @return 提交后的解决方案
	 */
	FeedbackTriggerSolution submit(String id);

	/**
	 * 根据解决人ID查找解决方案列表
	 * @param solverId 解决人ID
	 * @return 解决方案列表
	 */
	List<FeedbackTriggerSolution> findBySolverId(String solverId);

	/**
	 * 根据触发记录ID和发送记录ID查找唯一解决方案
	 * @param triggerRecordId 触发记录ID
	 * @param triggerSendId 发送记录ID
	 * @return 解决方案（唯一对象）
	 */
	FeedbackTriggerSolution findByTriggerRecordIdAndTriggerSendId(String triggerRecordId, String triggerSendId);

	/**
	 * 退回解决方案
	 * @param id 解决方案ID
	 * @param request 退回请求对象，包含退回原因、退回人ID和退回人名称
	 * @return 退回后的解决方案
	 */
	FeedbackTriggerSolution reject(String id, com.github.cret.web.oee.domain.request.SolutionRejectRequest request);

}
