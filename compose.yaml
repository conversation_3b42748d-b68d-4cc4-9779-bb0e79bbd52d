# 定义自定义网络
networks:
  oee-net:
    driver: bridge
    ipam:
      config:
        - subnet: "*************/24"  # 使用自定义子网（可替换为其他非冲突网段）
          gateway: "*************"

services:
  oee-backend:
    image: "*************:3000/oee/oee-backend:0.5"
    ports:
      - "8091:8091"
    networks:
      oee-net:
        ipv4_address: *************0  # 可选：为容器指定固定 IP
    restart: unless-stopped

  oee-frontend:
    image: "*************:3000/oee/oee-frontend:0.3"
    ports:
      - "3000:80"
    networks:
      oee-net:
        ipv4_address: **************  # 可选：为容器指定固定 IP
    restart: unless-stopped

# 如果启用 MongoDB，也需连接到自定义网络
#   mongodb:
#     image: "mongo:latest"
#     environment:
#       - "MONGO_INITDB_DATABASE=mydatabase"
#       - "MONGO_INITDB_ROOT_PASSWORD=secret"
#       - "MONGO_INITDB_ROOT_USERNAME=root"
#     ports:
#       - "27017:27017"
#     networks:
#       oee-net:
#         ipv4_address: **************  # 可选：固定 IP