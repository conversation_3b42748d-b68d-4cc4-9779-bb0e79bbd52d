package com.github.cret.web.oee.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.oee.config.ProxyConfig;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.device.StatusResponse;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.repository.DeviceRepository;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.utils.BuilderUtil;

@Service
public class DeviceServiceImpl implements DeviceService {

	private final DeviceRepository repository;

	private final MongoTemplate mongoTemplate;

	private final RestTemplate restTemplate;

	private final ProxyConfig proxyConfig;

	public DeviceServiceImpl(DeviceRepository repository, MongoTemplate mongoTemplate, RestTemplate restTemplate,
			ProxyConfig proxyConfig) {
		this.repository = repository;
		this.mongoTemplate = mongoTemplate;
		this.restTemplate = restTemplate;
		this.proxyConfig = proxyConfig;
	}

	@Override
	public Device getByCode(String code) {
		return repository.findFirstByCode(code).orElseThrow();
	}

	/**
	 * 获取指定线体下所有设备
	 * @param lineCode 线体编码
	 * @return 设备列表
	 */
	@Override
	public List<Device> getAllDevices(String code) {
		List<Device> list = repository.findByLineIdOrderBySort(code);
		return list.stream().filter(device -> device.getEnable() == 1).toList();
	}

	/**
	 * 获取指定线体下指定分类的设备列表
	 * @param lineCode 线体编码
	 * @param category 设备分类
	 * @return 设备列表
	 */
	@Override
	public List<Device> getDevicesByCategory(String code, DeviceCategory category) {
		return repository.findByLineIdAndCategory(code, category)
			.stream()
			.filter(device -> device.getEnable() == 1)
			.sorted((a, b) -> Integer.compare(a.getSort(), b.getSort()))
			.collect(Collectors.toList());
	}

	@Override
	public List<Device> findFirstDeviceByLineIdAndType(String lineCode, DeviceCategory deviceCategory) {
		// 匹配条件：线体编码和设备类型
		MatchOperation matchOperation = Aggregation
			.match(Criteria.where("line_id").is(lineCode).and("category").is(deviceCategory).and("enable").is(1));

		// 按轨道分组，并获取每个轨道的第一个设备
		SortOperation sortOperation = Aggregation.sort(Sort.Direction.ASC, "sort");
		GroupOperation groupOperation = Aggregation.group("track")
			.first("id")
			.as("id")
			.first("name")
			.as("name")
			.first("code")
			.as("code")
			.first("type")
			.as("type")
			.first("category")
			.as("category")
			.first("track")
			.as("track")
			.first("line_id")
			.as("line_id")
			.first("sort")
			.as("sort");

		// 聚合操作
		Aggregation aggregation = Aggregation.newAggregation(matchOperation, sortOperation, groupOperation);

		// 执行聚合查询
		AggregationResults<Device> results = mongoTemplate.aggregate(aggregation, "t_device", Device.class);

		return results.getMappedResults();
	}

	@Override
	public StatusResponse getClientStatus(String code) {
		// 获取设备
		Device device = repository.findFirstByCode(code)
			.orElseThrow(() -> SysErrEnum.NOT_FOUND.exception("设备不存在: " + code));

		// 构建目标URL
		String targetUrl = String.format("http://%s:%s/api/status", device.getClientIp(), device.getClientPort());

		// 构建代理URL
		String proxyUrl = String.format("%s?url=%s", proxyConfig.getServerUrl(), targetUrl);

		try {
			ResponseEntity<String> response = restTemplate.getForEntity(proxyUrl, String.class);
			if (response.getStatusCode().is2xxSuccessful()) {
				String responseBody = response.getBody();
				StatusResponse statusResponse = new StatusResponse();
				if (responseBody != null && responseBody.contains("\"status\":true")) {
					statusResponse.setStatus(1); // 在线
				}
				else {
					statusResponse.setStatus(0); // 离线
				}
				return statusResponse;
			}
			return BuilderUtil.builder(StatusResponse::new).with(StatusResponse::setStatus, -1).build();
		}
		catch (HttpClientErrorException e) {
			// 处理 HTTP 错误
			return BuilderUtil.builder(StatusResponse::new).with(StatusResponse::setStatus, -1).build();
		}
		catch (ResourceAccessException e) {
			// 处理网络连接错误
			return BuilderUtil.builder(StatusResponse::new).with(StatusResponse::setStatus, -2).build();
		}
		catch (Exception e) {
			// 处理其他未知错误
			return BuilderUtil.builder(StatusResponse::new).with(StatusResponse::setStatus, -3).build();
		}
	}

	@Override
	public Device save(Device device) {
		return mongoTemplate.save(device);
	}

	@Override
	public Device findById(String id) {
		return mongoTemplate.findById(id, Device.class);
	}

	@Override
	public PageList<Device> page(PageableParam<Device> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("name", GenericPropertyMatchers.contains())
			.withMatcher("code", GenericPropertyMatchers.contains())
			.withMatcher("lineId", GenericPropertyMatchers.contains())
			// 显式忽略 _class 属性
			.withIgnorePaths("_class");
		Example<Device> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<Device> findList(Device param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("name", GenericPropertyMatchers.contains())
			.withMatcher("code", GenericPropertyMatchers.contains())
			.withMatcher("lineId", GenericPropertyMatchers.exact());
		Example<Device> example = Example.of(param, matcher);
		return repository.findAll(example);
	}

	@Override
	public List<Device> findAll() {
		return mongoTemplate.findAll(Device.class);
	}

	@Override
	public void deleteById(String id) {
		Device device = findById(id);
		if (device != null) {
			mongoTemplate.remove(device);
		}
	}

	@Override
	public void batchDelete(List<String> ids) {
		if (ids == null || ids.isEmpty()) {
			return;
		}
		Query query = Query.query(Criteria.where("_id").in(ids));
		mongoTemplate.remove(query, Device.class);
	}

}
