package com.github.cret.web.oee.document.feedback;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 快返触发解决方案发送表
 */
@Document("t_feedback_trigger_solution_send")
public class FeedbackTriggerSolutionSend {

	@Id
	private String id;

	// 预期发送时间
	@Field(name = "expected_send_time")
	private Date expectedSendTime;

	// 发送时间
	@Field(name = "send_time")
	private Date sendTime;

	// 发送人
	@Field(name = "send_user_id")
	private List<String> sendUserId;

	// 告知人
	@Field(name = "report_user_id")
	private List<String> reportUserId;

	// 发送结果
	@Field(name = "send_result")
	private String sendResult;

	// 解决方案ID
	@Field(name = "trigger_solution_id")
	private String triggerSolutionId;

	// 触发记录ID
	@Field(name = "trigger_record_id")
	private String triggerRecordId;

	// 发送记录ID
	@Field(name = "trigger_send_id")
	private String triggerSendId;

	// 发送信息
	@Field(name = "send_info")
	private String sendInfo;

	// 是否发送
	@Field(name = "send_status")
	private Boolean sendStatus;

	// 创建时间
	@Field(name = "create_time")
	private Date createTime;

	// 更新时间
	@Field(name = "update_time")
	private Date updateTime;

	// 创建人
	@Field(name = "create_by")
	private String createBy;

	// 更新人
	@Field(name = "update_by")
	private String updateBy;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getExpectedSendTime() {
		return expectedSendTime;
	}

	public void setExpectedSendTime(Date expectedSendTime) {
		this.expectedSendTime = expectedSendTime;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public List<String> getSendUserId() {
		return sendUserId;
	}

	public void setSendUserId(List<String> sendUserId) {
		this.sendUserId = sendUserId;
	}

	public List<String> getReportUserId() {
		return reportUserId;
	}

	public void setReportUserId(List<String> reportUserId) {
		this.reportUserId = reportUserId;
	}

	public String getSendResult() {
		return sendResult;
	}

	public void setSendResult(String sendResult) {
		this.sendResult = sendResult;
	}

	public String getTriggerSolutionId() {
		return triggerSolutionId;
	}

	public void setTriggerSolutionId(String triggerSolutionId) {
		this.triggerSolutionId = triggerSolutionId;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getTriggerSendId() {
		return triggerSendId;
	}

	public void setTriggerSendId(String triggerSendId) {
		this.triggerSendId = triggerSendId;
	}

	public String getSendInfo() {
		return sendInfo;
	}

	public void setSendInfo(String sendInfo) {
		this.sendInfo = sendInfo;
	}

	public Boolean getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Boolean sendStatus) {
		this.sendStatus = sendStatus;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

}
