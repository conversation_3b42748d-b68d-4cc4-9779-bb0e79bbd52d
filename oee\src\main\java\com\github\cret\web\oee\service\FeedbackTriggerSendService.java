package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.document.feedback.ResponseConfig;

/**
 * 反馈触发发送服务接口
 *
 * 负责管理反馈触发发送记录，包括CRUD操作、状态管理、消息发送和定时任务处理。 超期策略：超过1分钟的记录标记为超期未发送，1分钟内的正常发送。
 */
public interface FeedbackTriggerSendService {

	// ==================== 基础CRUD操作 ====================

	/**
	 * 保存发送记录，支持新增和更新
	 */
	FeedbackTriggerSend save(FeedbackTriggerSend feedbackTriggerSend);

	/**
	 * 根据ID查找发送记录
	 */
	FeedbackTriggerSend findById(String id);

	/**
	 * 分页查询发送记录
	 */
	PageList<FeedbackTriggerSend> page(PageableParam<FeedbackTriggerSend> param);

	/**
	 * 根据条件查询发送记录列表
	 */
	List<FeedbackTriggerSend> findList(FeedbackTriggerSend param);

	/**
	 * 查询所有发送记录
	 */
	List<FeedbackTriggerSend> findAll();

	/**
	 * 根据ID删除发送记录
	 */
	void deleteById(String id);

	/**
	 * 批量删除发送记录
	 */
	void batchDelete(List<String> ids);

	// ==================== 业务查询方法 ====================

	/**
	 * 根据触发记录ID查找发送记录
	 */
	List<FeedbackTriggerSend> findByTriggerRecordId(String triggerRecordId);

	/**
	 * 根据发送状态查找发送记录
	 */
	List<FeedbackTriggerSend> findBySendStatus(Boolean sendStatus);

	/**
	 * 根据触发记录ID和发送状态查找发送记录
	 */
	List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, Boolean sendStatus);

	/**
	 * 查找到期且未发送的记录，用于定时任务
	 */
	List<FeedbackTriggerSend> findPendingSendRecords(Date expectedSendTime);

	/**
	 * 查找指定时间范围内的发送记录
	 */
	List<FeedbackTriggerSend> findByExpectedSendTimeBetween(Date startTime, Date endTime);

	// ==================== 状态更新方法 ====================

	/**
	 * 更新发送状态，sendStatus为true时自动设置发送时间
	 */
	FeedbackTriggerSend updateSendStatus(String id, Boolean sendStatus, String sendResult);

	/**
	 * 标记为已发送
	 */
	FeedbackTriggerSend markAsSent(String id, String sendResult);

	/**
	 * 标记为发送失败，可重试
	 */
	FeedbackTriggerSend markAsFailed(String id, String sendResult);

	// ==================== 核心业务方法 ====================

	/**
	 * 执行发送操作
	 *
	 * 发送企业微信通知给响应人（edit链接）和通知人（view链接）， 限制接收人总数不超过1000人。
	 */
	String performSend(FeedbackTriggerSend sendRecord);

	/**
	 * 发送企业微信通知
	 */
	String send(FeedbackTriggerSend sendRecord);

	/**
	 * 处理定时发送任务
	 *
	 * 查找到期且未发送的记录并执行发送。 超过1分钟的记录标记为超期未发送，1分钟内的正常发送。
	 */
	void processScheduledSends();

	/**
	 * 创建反馈触发发送记录（为每个用户创建单独记录）
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 */
	void createFeedbackTriggerSend(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig);

}
