package com.github.cret.web.oee.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 反馈触发发送定时任务服务
 */
@Service
public class FeedbackTriggerSendScheduleService {

	private static final Logger logger = LoggerFactory.getLogger(FeedbackTriggerSendScheduleService.class);

	private final FeedbackTriggerSendService feedbackTriggerSendService;

	public FeedbackTriggerSendScheduleService(FeedbackTriggerSendService feedbackTriggerSendService) {
		this.feedbackTriggerSendService = feedbackTriggerSendService;
	}

	/**
	 * 每分钟执行一次，处理到期的反馈触发发送记录
	 */
	@Scheduled(fixedRate = 60000) // 60秒 = 1分钟
	public void processScheduledFeedbackSends() {
		try {
			logger.debug("开始执行反馈触发发送定时任务");
			feedbackTriggerSendService.processScheduledSends();
			logger.debug("反馈触发发送定时任务执行完成");
		}
		catch (Exception e) {
			logger.error("反馈触发发送定时任务执行失败", e);
		}
	}

}
