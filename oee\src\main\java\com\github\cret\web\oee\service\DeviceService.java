package com.github.cret.web.oee.service;

import java.util.List;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.device.StatusResponse;
import com.github.cret.web.oee.enums.DeviceCategory;

public interface DeviceService {

	// 基础CRUD
	Device save(Device device);

	Device findById(String id);

	PageList<Device> page(PageableParam<Device> param);

	List<Device> findList(Device param);

	List<Device> findAll();

	void deleteById(String id);

	void batchDelete(List<String> ids);

	// 已有的特定业务方法
	Device getByCode(String code);

	List<Device> getAllDevices(String code);

	List<Device> getDevicesByCategory(String code, DeviceCategory category);

	List<Device> findFirstDeviceByLineIdAndType(String lineCode, DeviceCategory deviceCategory);

	StatusResponse getClientStatus(String code);

}
