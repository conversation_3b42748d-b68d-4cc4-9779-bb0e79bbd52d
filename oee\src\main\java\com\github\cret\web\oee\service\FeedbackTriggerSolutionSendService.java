package com.github.cret.web.oee.service;

import java.util.Date;
import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolutionSend;
import com.github.cret.web.oee.document.feedback.NoticeUser;

/**
 * 反馈触发解决方案发送服务接口
 */
public interface FeedbackTriggerSolutionSendService {

	// 基础CRUD操作
	FeedbackTriggerSolutionSend save(FeedbackTriggerSolutionSend solutionSend);

	FeedbackTriggerSolutionSend findById(String id);

	PageList<FeedbackTriggerSolutionSend> page(PageableParam<FeedbackTriggerSolutionSend> param);

	List<FeedbackTriggerSolutionSend> findList(FeedbackTriggerSolutionSend param);

	List<FeedbackTriggerSolutionSend> findAll();

	void deleteById(String id);

	void batchDelete(List<String> ids);

	// 业务查询方法
	/**
	 * 根据解决方案ID查找发送记录
	 * @param triggerSolutionId 解决方案ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByTriggerSolutionId(String triggerSolutionId);

	/**
	 * 根据发送状态查找发送记录
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findBySendStatus(Boolean sendStatus);

	/**
	 * 根据解决方案ID和发送状态查找发送记录
	 * @param triggerSolutionId 解决方案ID
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByTriggerSolutionIdAndSendStatus(String triggerSolutionId,
			Boolean sendStatus);

	/**
	 * 查找待发送的记录
	 * @param expectedSendTime 预期发送时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findPendingSendRecords(Date expectedSendTime);

	/**
	 * 根据预期发送时间范围查找发送记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByExpectedSendTimeBetween(Date startTime, Date endTime);

	/**
	 * 根据发送人查找发送记录
	 * @param userId 用户ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findBySendUserId(String userId);

	/**
	 * 根据告知人查找发送记录
	 * @param userId 用户ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByReportUserId(String userId);

	/**
	 * 更新发送状态
	 * @param id 记录ID
	 * @param sendStatus 发送状态
	 * @param sendResult 发送结果
	 * @return 更新后的记录
	 */
	FeedbackTriggerSolutionSend updateSendStatus(String id, Boolean sendStatus, String sendResult);

	/**
	 * 标记为已发送
	 * @param id 记录ID
	 * @param sendResult 发送结果
	 * @return 更新后的记录
	 */
	FeedbackTriggerSolutionSend markAsSent(String id, String sendResult);

	/**
	 * 标记为发送失败
	 * @param id 记录ID
	 * @param sendResult 发送结果
	 * @return 更新后的记录
	 */
	FeedbackTriggerSolutionSend markAsFailed(String id, String sendResult);

	/**
	 * 执行发送操作
	 * @param sendRecord 发送记录
	 * @return 发送结果
	 */
	String performSend(FeedbackTriggerSolutionSend sendRecord);

	/**
	 * 处理定时发送任务 查找所有到期且未发送的记录并执行发送
	 */
	void processScheduledSends();

	/**
	 * 创建解决方案通知记录
	 * @param savedSolution 已保存的解决方案对象
	 * @param noticeUsers 通知用户列表
	 * @return 创建的发送记录列表，每个用户一条记录
	 */
	List<FeedbackTriggerSolutionSend> createSolutionNotification(FeedbackTriggerSolution savedSolution,
			List<NoticeUser> noticeUsers);

	/**
	 * 创建解决方案退回通知记录
	 * @param rejectedSolution 被退回的解决方案对象
	 * @return 创建的发送记录，通知处理人
	 */
	FeedbackTriggerSolutionSend createRejectNotification(FeedbackTriggerSolution rejectedSolution);

}
