package com.github.cret.web.oee.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;

@Repository
public interface FeedbackTriggerSolutionRepository extends MongoRepository<FeedbackTriggerSolution, String> {

	/**
	 * 根据触发记录ID查找解决方案
	 * @param triggerRecordId 触发记录ID
	 * @return 解决方案列表
	 */
	List<FeedbackTriggerSolution> findByTriggerRecordId(String triggerRecordId);

	/**
	 * 根据解决人ID查找解决方案
	 * @param solverId 解决人ID
	 * @return 解决方案列表
	 */
	List<FeedbackTriggerSolution> findBySolverId(String solverId);

	/**
	 * 根据触发记录ID和提交状态查找解决方案
	 * @param triggerRecordId 触发记录ID
	 * @param submited 提交状态
	 * @return 解决方案列表
	 */
	List<FeedbackTriggerSolution> findByTriggerRecordIdAndSubmitted(String triggerRecordId, <PERSON><PERSON><PERSON> submitted);

	/**
	 * 根据触发记录ID和发送记录ID查找唯一解决方案
	 * @param triggerRecordId 触发记录ID
	 * @param triggerSendId 发送记录ID
	 * @return 解决方案（唯一对象）
	 */
	FeedbackTriggerSolution findByTriggerRecordIdAndTriggerSendId(String triggerRecordId, String triggerSendId);

}
