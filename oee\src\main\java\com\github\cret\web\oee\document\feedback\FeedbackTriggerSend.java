package com.github.cret.web.oee.document.feedback;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 快返触发发送表
 */
@Document("t_feedback_trigger_send")
public class FeedbackTriggerSend {

	@Id
	private String id;

	// 预期发送时间
	@Field(name = "expected_send_time")
	private Date expectedSendTime;

	// 发送时间
	@Field(name = "send_time")
	private Date sendTime;

	// 响应人（保留用于向后兼容）
	@Field(name = "send_user_id")
	private List<Responder> responders;

	// 告知人（保留用于向后兼容）
	@Field(name = "report_user_id")
	private List<Reporter> reporters;

	// 单个用户ID（新增字段）
	@Field(name = "user_id")
	private String userId;

	// 单个用户名称（新增字段）
	@Field(name = "user_name")
	private String userName;

	// 用户类型：responder-响应人, reporter-告知人（新增字段）
	@Field(name = "user_type")
	private String userType;

	// 发送结果
	@Field(name = "send_result")
	private String sendResult;

	// 触发记录ID
	@Field(name = "trigger_record_id")
	private String triggerRecordId;

	// 发送信息
	@Field(name = "send_info")
	private String sendInfo;

	// 是否发送
	@Field(name = "send_status")
	private Boolean sendStatus;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getExpectedSendTime() {
		return expectedSendTime;
	}

	public void setExpectedSendTime(Date expectedSendTime) {
		this.expectedSendTime = expectedSendTime;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public List<Responder> getResponders() {
		return responders;
	}

	public void setResponders(List<Responder> responders) {
		this.responders = responders;
	}

	public List<Reporter> getReporters() {
		return reporters;
	}

	public void setReporters(List<Reporter> reporters) {
		this.reporters = reporters;
	}

	public String getSendResult() {
		return sendResult;
	}

	public void setSendResult(String sendResult) {
		this.sendResult = sendResult;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getSendInfo() {
		return sendInfo;
	}

	public void setSendInfo(String sendInfo) {
		this.sendInfo = sendInfo;
	}

	public Boolean getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Boolean sendStatus) {
		this.sendStatus = sendStatus;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

}
