package com.github.cret.web.oee.controller;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.common.domain.ApiResponse;
import com.github.cret.web.oee.domain.wx.Department;
import com.github.cret.web.oee.domain.wx.DepartmentListRes;
import com.github.cret.web.oee.domain.wx.TreeNode;
import com.github.cret.web.oee.domain.wx.WxUser;
import com.github.cret.web.oee.domain.wx.WxUserListRes;
import com.github.cret.web.oee.config.WxWorkConfig;

@RestController
@RequestMapping("/wx-server")
public class WxServerController {

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private WxWorkConfig wxWorkConfig;

	@GetMapping("/getDepartmentList/cost-analysis")
	public ResponseEntity<List<TreeNode>> getDepartmentList() {
		String url = wxWorkConfig.getSendSeverUrl() + "/getDepartmentList/cost-analysis";
		ApiResponse<DepartmentListRes> response = restTemplate
			.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<ApiResponse<DepartmentListRes>>() {
			})
			.getBody();

		if (response == null || response.getData() == null) {
			return ResponseEntity.internalServerError().build();
		}

		List<Department> departments = response.getData().getDepartment();
		List<TreeNode> tree = buildTree(departments);

		return ResponseEntity.ok(tree);
	}

	@GetMapping("/getUserList/cost-analysis")
	public ResponseEntity<List<WxUser>> getUserList(@RequestParam("departmentId") Integer departmentId) {
		String url = wxWorkConfig.getSendSeverUrl() + "/getUserList/cost-analysis?departmentId=" + departmentId;
		ApiResponse<WxUserListRes> response = restTemplate
			.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<ApiResponse<WxUserListRes>>() {
			})
			.getBody();

		if (response == null || response.getData() == null) {
			return ResponseEntity.internalServerError().build();
		}

		return ResponseEntity.ok(response.getData().getUserlist());
	}

	@GetMapping("/getUserTreeList/cost-analysis")
	public ResponseEntity<List<TreeNode>> getUserTreeList(@RequestParam("departmentId") Integer departmentId) {
		String url = wxWorkConfig.getSendSeverUrl() + "/getUserList/cost-analysis?departmentId=" + departmentId;
		ApiResponse<WxUserListRes> response = restTemplate
			.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<ApiResponse<WxUserListRes>>() {
			})
			.getBody();

		if (response == null || response.getData() == null) {
			return ResponseEntity.internalServerError().build();
		}

		List<WxUser> users = response.getData().getUserlist();
		List<TreeNode> tree = buildUserTree(users, departmentId);

		return ResponseEntity.ok(tree);
	}

	private List<TreeNode> buildTree(List<Department> departments) {
		Map<Integer, TreeNode> nodeMap = new HashMap<>();

		// 第一次遍历：创建所有部门对应的TreeNode
		for (Department dept : departments) {
			TreeNode node = new TreeNode();
			node.setId(dept.getId());
			node.setLabel(dept.getName());
			node.setOrder(dept.getOrder());
			nodeMap.put(dept.getId(), node);
		}

		// 查找根节点（id=1）
		TreeNode root = nodeMap.get(1);
		if (root == null) {
			return Collections.emptyList();
		}

		// 第二次遍历：建立父子关系
		for (Department dept : departments) {
			TreeNode node = nodeMap.get(dept.getId());
			Integer parentId = dept.getParentid();

			if (parentId != null) {
				TreeNode parentNode = nodeMap.get(parentId);
				if (parentNode != null) {
					parentNode.getChildren().add(node);
				}
			}
		}

		// 按order排序子节点
		sortChildren(root);

		// 生成层级Key
		generateKeys(root, "0");

		// 设置data和icon
		setNodeDataAndIcons(root);

		// 设置根节点属性
		root.setKey("1");
		root.setData(root.getId().toString());

		return Collections.singletonList(root);
	}

	private void sortChildren(TreeNode node) {
		node.getChildren().sort(Comparator.comparingInt(TreeNode::getOrder).reversed());
		for (TreeNode child : node.getChildren()) {
			sortChildren(child);
		}
	}

	private void generateKeys(TreeNode node, String parentKey) {
		List<TreeNode> children = node.getChildren();
		for (int i = 0; i < children.size(); i++) {
			TreeNode child = children.get(i);
			String childKey = parentKey + "-" + i;
			child.setKey(childKey);
			generateKeys(child, childKey);
		}
	}

	private void setNodeDataAndIcons(TreeNode node) {
		if (node.getChildren().isEmpty()) {
			node.setData(node.getId().toString());
			node.setIcon("pi pi-fw pi-folder");
		}
		else {
			node.setData(node.getId().toString());
			node.setIcon("pi pi-fw pi-folder");
		}
		// 递归处理子节点
		for (TreeNode child : node.getChildren()) {
			setNodeDataAndIcons(child);
		}
	}

	/**
	 * 构建用户树形结构
	 * @param users 用户列表
	 * @param departmentId 部门ID
	 * @return 树形结构
	 */
	private List<TreeNode> buildUserTree(List<WxUser> users, Integer departmentId) {
		// 创建根节点
		TreeNode root = new TreeNode();
		root.setId(departmentId);
		root.setLabel("用户列表");
		root.setKey("root");
		root.setData(departmentId.toString());
		root.setIcon("pi pi-fw pi-folder");

		// 为每个用户创建子节点
		for (int i = 0; i < users.size(); i++) {
			WxUser user = users.get(i);
			TreeNode userNode = new TreeNode();
			userNode.setId(Integer.valueOf(i));
			userNode.setLabel(user.getName());
			userNode.setKey("user-" + i);
			userNode.setData(user.getUserid());
			userNode.setIcon("pi pi-fw pi-user");
			userNode.setOrder(i);

			// 添加到根节点
			root.getChildren().add(userNode);
		}

		return Collections.singletonList(root);
	}

}