package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.oee.calculator.DeviceCtCalculator;
import com.github.cret.web.oee.calculator.HourlyOutputCalculator;
import com.github.cret.web.oee.calculator.OeeCalculator;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.document.analyze.TargetOee;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.domain.analyze.DeviceCtInfo;
import com.github.cret.web.oee.domain.analyze.DeviceDefectType;
import com.github.cret.web.oee.domain.analyze.DeviceProductModel;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.ProductionLineInfo;
import com.github.cret.web.oee.domain.analyze.ProductionLineResult;
import com.github.cret.web.oee.domain.analyze.calculate.DefectTypeInfo;
import com.github.cret.web.oee.domain.analyze.calculate.HourlyRunTimeGroup;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.result.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.result.DefectTypeResult;
import com.github.cret.web.oee.domain.analyze.result.HourlyOutput;
import com.github.cret.web.oee.domain.analyze.result.HourlyOutputList;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.enums.DeviceType;
import com.github.cret.web.oee.repository.TargetOeeRepository;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.BaseAnalyzeService;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.ProductService;
import com.github.cret.web.oee.service.ProductionLineService;
import com.github.cret.web.oee.service.TheoreticalOutputService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.system.document.Dict;
import com.github.cret.web.system.domain.DictItem;
import com.github.cret.web.system.repository.DictRepository;

/**
 * 分析服务实现类
 */
@Service
public class AnalyzeServiceImpl implements AnalyzeService {

	private final DeviceService deviceService;

	private final ProductionLineService productionLineService;

	private final ProductService productService;

	private final TheoreticalOutputService theoreticalOutputService;

	private final TargetOeeRepository targetOeeRepository;

	private final DictRepository dictRepository;

	private final MongoTemplate mongoTemplate;

	private final BaseAnalyzeService baseAnalyzeService;

	public AnalyzeServiceImpl(DeviceService deviceService, ProductionLineService productionLineService,
			ProductService productService, TheoreticalOutputService theoreticalOutputService,
			TargetOeeRepository targetOeeRepository, DictRepository dictRepository, MongoTemplate mongoTemplate,
			BaseAnalyzeService baseAnalyzeService) {
		this.deviceService = deviceService;
		this.productionLineService = productionLineService;
		this.productService = productService;
		this.theoreticalOutputService = theoreticalOutputService;
		this.targetOeeRepository = targetOeeRepository;
		this.dictRepository = dictRepository;
		this.mongoTemplate = mongoTemplate;
		this.baseAnalyzeService = baseAnalyzeService;
	}

	/**
	 * 获取当前生产的产品型号信息
	 * @param query 查询条件
	 * @return 产品信息对象，包含产品型号和样本标记
	 */
	@Override
	public ProductionLineResult getCurrentProduction(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		// 获取产线设备
		List<Device> allLineDevices = deviceService.getAllDevices(query.getCode());
		Map<DeviceCategory, List<Device>> devicesByCategory = allLineDevices.stream()
			.collect(Collectors.groupingBy(Device::getCategory));

		// 处理SMT设备逻辑
		List<Device> smtEquipment = Optional.ofNullable(devicesByCategory.get(DeviceCategory.SMT))
			.orElse(Collections.emptyList());

		// 按轨道分组并获取首序设备
		Map<String, Device> primarySmtDevices = smtEquipment.stream()
			.collect(Collectors.groupingBy(device -> device.getTrackEnum().getCode(),
					Collectors.collectingAndThen(Collectors.toList(),
							devices -> devices.stream().min(Comparator.comparing(Device::getSort)).orElse(null))));

		// 构建产线生产状态
		final List<ProductionLineInfo> productionLineInfos = baseAnalyzeService.getCurrentProduction(query,
				primarySmtDevices);

		// 装配最终结果
		return BuilderUtil.builder(ProductionLineResult::new)
			.with(ProductionLineResult::setProductionLineInfos, productionLineInfos)
			.with(ProductionLineResult::setProductionLineType, productionLine.getType())
			.build();
	}

	@Override
	public OeeResult getLineOee(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		OeeResult oeeResult = new OeeResult();

		// 获取线体下所有贴片机设备
		List<Device> smtDevices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.SMT);

		// 按轨道分组并获取首序设备
		Map<String, Device> primarySmtDevices = smtDevices.stream()
			.collect(Collectors.groupingBy(device -> device.getTrackEnum().getCode(),
					Collectors.collectingAndThen(Collectors.toList(),
							devices -> devices.stream().min(Comparator.comparing(Device::getSort)).orElse(null))));

		// 按轨道分组并获取末序设备
		// Map<String, Device> lastSmtDevices = smtDevices.stream()
		// .collect(Collectors.groupingBy(device -> device.getTrackEnum().getCode(),
		// Collectors.collectingAndThen(Collectors.toList(),
		// devices ->
		// devices.stream().max(Comparator.comparing(Device::getSort)).orElse(null))));

		// 获取生产数据
		Map<String, List<ProductionData>> productData = baseAnalyzeService.getProductData(query, smtDevices);

		// 获取换线信息
		LineChangeoverInfo linChangeoverInfo = baseAnalyzeService.getLineChangeoverInfo(query, primarySmtDevices);

		// 计算换线次数
		oeeResult.setChangeoverNum(String.valueOf(linChangeoverInfo.getChangeoverNum()));

		// 计算换线时间(s)
		oeeResult.setChangeoverTime(String.format("%.2f", (double) linChangeoverInfo.getChangeoverTime() / 3600));

		// 计算计划时间（保留两位小数）
		double calculatePlanTime = OeeCalculator.calculatePlanTime(query);
		oeeResult.setPlanTime(String.format("%.2f", calculatePlanTime / 3600));

		// 计算实际计划时间
		double calculateActualPlanTime = OeeCalculator.calculateActualPlanTime(linChangeoverInfo.getChangeoverTime(),
				calculatePlanTime);
		oeeResult.setActualPlanTime(String.format("%.2f", calculateActualPlanTime / 3600));

		// 计算运行时间
		double calculateRunTime = OeeCalculator.calculateRunTime(productData, productionLine);
		oeeResult.setRunTime(String.format("%.2f", calculateRunTime / 3600));

		// 计算停机时间
		double calculateStopTime = OeeCalculator.calculateStopTime(productData, productionLine);

		// 停机时间加上换线的停机时间
		// calculateStopTime += linChangeoverInfo.getChangeoverStopTime();
		oeeResult.setStopTime(String.format("%.2f", calculateStopTime / 3600));

		// 计算实际生产数量
		Integer calculateActualProduction = OeeCalculator.calculateActualProduction(productData, productionLine);
		oeeResult.setActualBoard(String.valueOf(calculateActualProduction));

		// 计算理论生产数量
		Integer calculatePlanProduction = OeeCalculator.calculatePlanProduction(productData, productionLine);
		oeeResult.setPlanBoard(String.valueOf(calculatePlanProduction));

		// 计算实际生产点数
		Integer calculateActualProuductionPoints = OeeCalculator.calculateActualProuductionPoints(productData,
				productService);
		oeeResult.setActualBoardPoints(String.valueOf(calculateActualProuductionPoints));

		// 计算理论生产点数
		Integer calculatePlanProuductionPoints = OeeCalculator.calculatePlanProuductionPoints(productData,
				productService, productionLine);
		oeeResult.setPlanBoardPoints(String.valueOf(calculatePlanProuductionPoints));

		// 计算不良品数
		Integer calculateDefectCount = baseAnalyzeService.calculateDefectCount(query);
		oeeResult.setDefectCount(String.valueOf(calculateDefectCount));

		// 计算稼动率
		double calculateAvailability = OeeCalculator.calculateAvailability(calculateRunTime, calculateStopTime);
		oeeResult.setAvailability(String.format("%.2f", calculateAvailability * 100));

		// 计算运行效率
		double calculatePerformance = OeeCalculator.calculatePerformance(calculateActualProduction,
				calculatePlanProduction);
		oeeResult.setPerformance(String.format("%.2f", calculatePerformance * 100));

		// 计算良品率
		double calculateQuality = baseAnalyzeService.calculateQuality(query, calculateDefectCount, productData);
		oeeResult.setQuality(String.format("%.2f", calculateQuality * 100));

		// 计算OEE
		double calculateOee = OeeCalculator.calculateOee(calculatePerformance, calculateAvailability, calculateQuality);
		oeeResult.setOee(String.format("%.2f", calculateOee * 100));

		// 获取目标OEE
		List<TargetOee> targetOee = targetOeeRepository.findAll();
		if (!targetOee.isEmpty()) {
			oeeResult.setAvailabilityTarget(String.format("%.2f", targetOee.get(0).getAvailability() * 100));
			oeeResult.setPerformanceTarget(String.format("%.2f", targetOee.get(0).getPerformance() * 100));
			oeeResult.setQualityTarget(String.format("%.2f", targetOee.get(0).getQuality() * 100));
			oeeResult.setOeeTarget(String.format("%.2f", targetOee.get(0).getOee() * 100));
		}

		return oeeResult;
	}

	/**
	 * 获取设备CT
	 * @param query 查询条件
	 * @return 设备CT信息列表
	 * @throws UnsupportedOperationException 当未找到SMT设备时抛出
	 */
	@Override
	public List<DeviceCtInfo> getDevicesCt(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		// 获取指定线体下所有设备
		List<Device> allDevices = deviceService.getAllDevices(query.getCode());
		List<DeviceProductModel> currentProduction = baseAnalyzeService.collectDeviceProductModels(allDevices, query);
		// 获取每个设备的平均CT和每个设备理论CT
		List<DeviceCtInfo> deviceCt = getDeviceCt(query, allDevices, currentProduction);
		// 标记瓶颈工序
		markBottleneckDevice(deviceCt);
		return deviceCt;
	}

	@Override
	public HourlyOutputList getHourlyOutput(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		// 获取SMT设备并验证
		List<Device> devicesByCategory = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.SMT);
		if (devicesByCategory.isEmpty()) {
			return new HourlyOutputList();
		}

		// 获取实际CT最大的贴片机
		Device maxCtDevice = getSmtByMaxCt(query);
		if (maxCtDevice == null) {
			return new HourlyOutputList();
		}

		// 构建结果对象
		HourlyOutputList result = new HourlyOutputList();
		result.setActualOutputs(getHourlyActualOutput(maxCtDevice, query));

		// 获取并设置理论产出
		List<HourlyOutput> theoreticalOutputs = getHourlyTheoreticalOutput(maxCtDevice, query);
		result.setTheoreticalOutputs(theoreticalOutputs);

		// 设置当前产品理论产出
		result.setCurrentProductTheoreticalOutput(
				!theoreticalOutputs.isEmpty() ? theoreticalOutputs.get(theoreticalOutputs.size() - 1).getCount() : 0);

		return result;
	}

	/**
	 * 获取每小时实际产出
	 * @param device 设备信息
	 * @param query 查询条件
	 * @return 每小时实际产出列表
	 */
	private List<HourlyOutput> getHourlyActualOutput(Device device, AnalyzeQuery query) {
		return switch (device.getType()) {
			case smt_npm_reporter -> HourlyOutputCalculator.getNpmSmtHourlyOutput(device, query, mongoTemplate);
			case smt_npm_reporter2 -> HourlyOutputCalculator.getNpmSmtHourlyOutput(device, query, mongoTemplate);
			case smt_samsung -> HourlyOutputCalculator.getSamsungSmtHourlyOutput(device, query, mongoTemplate);
			case smt_yamaha -> HourlyOutputCalculator.getYamahaSmtHourlyOutput(device, query, mongoTemplate);
			default -> throw new UnsupportedOperationException("该设备类型未实现每小时实际产出查询");
		};
	}

	/**
	 * 获取每小时理论产出
	 * @param device 设备信息
	 * @param query 查询条件
	 * @return 每小时理论产出列表
	 */
	private List<HourlyOutput> getHourlyTheoreticalOutput(Device device, AnalyzeQuery query) {
		// 获取每小时运行时间组并按小时分组
		List<HourlyRunTimeGroup> hourlyRunTimeGroups;
		if (Objects.requireNonNull(device.getType()) == DeviceType.smt_samsung) {
			hourlyRunTimeGroups = HourlyOutputCalculator.getSamsungSmtHourlyRunTime(device, query, mongoTemplate);
		}
		else {
			hourlyRunTimeGroups = HourlyOutputCalculator.getNpmSmtHourlyRunTime(device, query, mongoTemplate);
		}
		Map<String, List<HourlyRunTimeGroup>> hourlyGroups = hourlyRunTimeGroups.stream()
			.collect(Collectors.groupingBy(HourlyRunTimeGroup::getHour));

		List<HourlyOutput> theoreticalOutputs = new ArrayList<>();

		for (Map.Entry<String, List<HourlyRunTimeGroup>> entry : hourlyGroups.entrySet()) {
			String hour = entry.getKey();
			List<HourlyRunTimeGroup> groups = entry.getValue();

			// 按首次生产时间排序
			groups.sort(Comparator.comparing(HourlyRunTimeGroup::getFirstTime));

			if (groups.size() == 1) {
				// 单产品情况处理
				processSingleProductHour(device, hour, groups.get(0), theoreticalOutputs);
			}
			else {
				// 多产品情况处理
				processMultipleProductsHour(device, hour, groups, theoreticalOutputs);
			}
		}

		// 按小时排序
		theoreticalOutputs.sort(Comparator.comparing(HourlyOutput::getTime));
		return theoreticalOutputs;
	}

	/**
	 * 处理单产品小时数据
	 * @param device 设备信息
	 * @param hour 小时
	 * @param group 运行时间组
	 * @param outputs 输出列表
	 */
	private void processSingleProductHour(Device device, String hour, HourlyRunTimeGroup group,
			List<HourlyOutput> outputs) {
		// 获取该产品的理论产出配置
		TheoreticalOutput theoreticalOutput = theoreticalOutputService.findByDeviceCodeAndProductModel(device.getCode(),
				group.getProductModel());

		if (theoreticalOutput != null && theoreticalOutput.getCt() > 0) {
			// 计算每小时理论产出：3600秒/节拍时间*拼板数
			double hourlyOutput = Math.floor(3600 / theoreticalOutput.getCt() * theoreticalOutput.getFlatNumber());

			HourlyOutput output = new HourlyOutput();
			output.setTime(hour);
			output.setCount((int) hourlyOutput);
			outputs.add(output);
		}
	}

	/**
	 * 处理多产品小时数据
	 * @param device 设备信息
	 * @param hour 小时
	 * @param groups 运行时间组列表
	 * @param outputs 输出列表
	 */
	private void processMultipleProductsHour(Device device, String hour, List<HourlyRunTimeGroup> groups,
			List<HourlyOutput> outputs) {
		double totalOutput = 0.0; // 总产出

		for (HourlyRunTimeGroup group : groups) {
			// 获取该产品的理论产出配置
			TheoreticalOutput theoreticalOutput = theoreticalOutputService
				.findByDeviceCodeAndProductModel(device.getCode(), group.getProductModel());

			if (theoreticalOutput != null && theoreticalOutput.getCt() > 0) {
				// 计算每小时理论产出：3600秒/节拍时间*拼板数
				totalOutput += Math
					.floor(group.getRunTime() / theoreticalOutput.getCt() * theoreticalOutput.getFlatNumber());
			}
		}

		// 创建并添加该小时的理论产出记录
		HourlyOutput output = new HourlyOutput();
		output.setTime(hour);
		output.setCount((int) Math.floor(totalOutput));
		outputs.add(output);
	}

	/**
	 * 获取不良类型统计
	 * @param query 查询条件
	 * @return 不良类型统计列表
	 */
	@Override
	public DefectTypeResult getDefectTypes(AnalyzeQuery query) {
		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		// 获取AOI设备
		List<Device> aoiDevices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.AOI);
		// 计算不良类型
		List<DeviceDefectType> defectTypes = baseAnalyzeService.getDefectTypes(query, aoiDevices);
		// 封装不良类型查询图表
		return getDefectTypeResult(defectTypes);
	}

	/**
	 * 获取TOP报警
	 * @param query 查询条件
	 * @return TOP报警列表
	 */
	@Override
	public List<AlarmInfo> getTopAlarms(AnalyzeQuery query) {
		// 线体编码
		String code = query.getCode();

		// 参数校验与基础数据准备
		query.validateTimeRange();
		ProductionLine productionLine = productionLineService.findProductionLineBycode(query.getCode());
		query.setLineType(productionLine.getType());

		Criteria criteria = Criteria.where("startTime")
			.gte(query.getStartTime())
			.and("endTime")
			.lte(query.getEndTime())
			.and("lineCode")
			.is(code);

		// 构建聚合查询
		Aggregation aggregation = Aggregation.newAggregation(
				// 1.时间过滤条件
				Aggregation.match(criteria),
				// 2.计算时长（毫秒）
				Aggregation.project("classification").andExpression("endTime - startTime").as("durationInMillis"),
				// 3. 按 classification 分组
				Aggregation.group("classification").sum("durationInMillis").as("totalDurationMillis"),
				// 4. 转换为分钟并保留两位小数
				Aggregation.project()
					.and("_id")
					.as("type")
					.andExpression("round(totalDurationMillis / (1000 * 60) * 100) / 100")
					.as("num"),
				Aggregation.sort(Sort.Direction.DESC, "num"), // 5. 倒序排序
				Aggregation.limit(5) // 6. 限制 Top N
		);

		// 执行聚合查询并映射结果
		AggregationResults<AlarmInfo> results = mongoTemplate.aggregate(aggregation, "t_abnormal", AlarmInfo.class);

		// 获取查询结果
		List<AlarmInfo> mappedResults = results.getMappedResults();

		// 根据字典获取异常类型
		Dict dict = dictRepository.findByCode("ABNORMAL_CODE")
			.orElseThrow(() -> SysErrEnum.NOT_FOUND.exception("异常类型不存在"));

		// 构建异常类型映射
		Map<String, String> typeNameMap = dict.getItemList()
			.stream()
			.collect(Collectors.toMap(DictItem::getValue, DictItem::getLabel, (v1, v2) -> v1));

		// 设置报警类型名称
		mappedResults.forEach(alarm -> alarm.setName(typeNameMap.getOrDefault(alarm.getType(), "未知")));

		// 获取生产信息
		// Map<String, List<ProductionData>> productData = getProductData(query);

		// Double npmsmtAlarm = AlarmCalculator.getNpmsmtAlarm(productData);

		return mappedResults;
	}

	/**
	 * 获取实际ct最大的贴片机
	 * @param query 查询条件
	 * @return 实际ct最大的贴片机
	 */
	private Device getSmtByMaxCt(AnalyzeQuery query) {
		// 获取贴片机设备
		List<Device> devices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.SMT);
		Device maxDevice = null;
		double maxCt = 0.0;
		for (Device device : devices) {
			double ct;
			if (Objects.requireNonNull(device.getType()) == DeviceType.smt_samsung) {
				ct = DeviceCtCalculator.getSamsungSmtCt(device, query, mongoTemplate);
			}
			else if (Objects.requireNonNull(device.getType() == DeviceType.smt_yamaha)) {
				ct = DeviceCtCalculator.getYamahaSmtCt(device, query, mongoTemplate);
			}
			else {
				ct = DeviceCtCalculator.getNpmSmtCt(device, query, mongoTemplate);
			}
			if (ct > maxCt) {
				maxCt = ct;
				maxDevice = device;
			}
		}
		return maxDevice;
	}

	private DefectTypeResult getDefectTypeResult(List<DeviceDefectType> defectTypes) {
		// 用于存储所有设备的缺陷类型统计结果
		List<Map<String, String>> allDefectTypes = new ArrayList<>();
		List<String> dimensions = new ArrayList<>();
		dimensions.add("type");
		List<Map<String, String>> heads = new ArrayList<>();

		// 遍历每个设备，获取缺陷类型数据
		for (int i = 0; i < defectTypes.size(); i++) {
			// 获取AOI不良类型数量
			List<DefectTypeInfo> aoiDefectTypes = defectTypes.get(i).getDefectTypeInfos();

			// 将 DefectTypeInfo 转换为 Map<String, String>
			for (DefectTypeInfo defectType : aoiDefectTypes) {
				// 查找是否已经存在该缺陷类型的记录
				Map<String, String> defectMap = allDefectTypes.stream()
					.filter(map -> map.get("type").equals(defectType.getType()))
					.findFirst()
					.orElseGet(() -> {
						// 使用 LinkedHashMap 确保 type 字段在最前面
						Map<String, String> newMap = new LinkedHashMap<>();
						newMap.put("type", defectType.getType()); // type 字段优先插入
						allDefectTypes.add(newMap);
						return newMap;
					});

				// 将当前设备的缺陷数量添加到对应的缺陷类型记录中
				defectMap.put("series" + (i + 1), defectType.getCount());
			}
			dimensions.add("series" + (i + 1));
		}

		// 生成表头信息
		for (int i = 0; i < defectTypes.size(); i++) {
			Map<String, String> map = new LinkedHashMap<>(); // 使用 LinkedHashMap 确保顺序
			map.put("type", "bar");
			map.put("name",
					(defectTypes.size() > 1 ? defectTypes.get(i).getDevice().getTrackEnum().getName() : "") + "不良数量");
			heads.add(map);
		}

		return BuilderUtil.builder(DefectTypeResult::new)
			.with(DefectTypeResult::setData, allDefectTypes)
			.with(DefectTypeResult::setDimensions, dimensions)
			.with(DefectTypeResult::setHeadMaps, heads)
			.build();
	}

	private List<DeviceCtInfo> getDeviceCt(AnalyzeQuery query, List<Device> allDevices,
			List<DeviceProductModel> deviceProductModels) {
		List<DeviceCtInfo> list = new ArrayList<>();

		// 修复1：构建映射时过滤非法对象和空值键
		Map<String, String> trackProductMap = deviceProductModels.stream()
			.filter(Objects::nonNull)
			.filter(dpm -> dpm.getDevice() != null)
			.filter(dpm -> dpm.getDevice().getCode() != null)
			.collect(Collectors.toMap(dpm -> dpm.getDevice().getCode(),
					dpm -> Optional.ofNullable(dpm.getProductModel()).orElse("UNKNOWN_MODEL"),
					(existing, replacement) -> replacement));

		for (Device device : allDevices) {
			if (device.getEnable() != 1)
				continue;

			DeviceCtInfo deviceCtInfo = BuilderUtil.builder(DeviceCtInfo::new)
				.with(DeviceCtInfo::setCode, device.getCode())
				.with(DeviceCtInfo::setType, device.getName())
				.with(DeviceCtInfo::setTrackName, device.getTrackEnum().getName())
				.build();

			String productModel = trackProductMap.get(device.getCode());
			if (productModel == null) {
				continue;
			}

			String originalProductModel = query.getProductModel();
			try {
				query.setProductModel(productModel);
				deviceCtInfo.setActualCt(DeviceCtCalculator.getDeviceCt(device, query, mongoTemplate));
			}
			finally {
				query.setProductModel(originalProductModel);
			}

			TheoreticalOutput theoreticalOutput = theoreticalOutputService
				.findByDeviceCodeAndProductModelContaining(device.getCode(), productModel);
			Optional.ofNullable(theoreticalOutput).map(TheoreticalOutput::getCt).ifPresent(ct -> {
				double roundedCt = Math.round(ct * 100.0) / 100.0;
				deviceCtInfo.setTheoreticalCt(roundedCt);
			});

			list.add(deviceCtInfo);
		}
		return list;
	}

	/**
	 * 标记瓶颈工序，按轨道分组进行标记
	 * @param deviceCtInfos 设备CT信息列表
	 */
	private void markBottleneckDevice(List<DeviceCtInfo> deviceCtInfos) {
		// 按轨道分组
		Map<String, List<DeviceCtInfo>> devicesByTrack = deviceCtInfos.stream()
			.collect(Collectors.groupingBy(DeviceCtInfo::getTrackName));

		// 遍历每个轨道的设备组
		for (List<DeviceCtInfo> trackDevices : devicesByTrack.values()) {
			// 找出该轨道最大的实际CT值
			double maxActualCt = trackDevices.stream().mapToDouble(DeviceCtInfo::getActualCt).max().orElse(0.0);

			// 只有当最大CT值大于0时才标记瓶颈工序
			if (maxActualCt > 0) {
				// 找到该轨道第一个具有最大CT值的设备，将其标记为瓶颈工序
				boolean bottleneckSet = false;
				for (DeviceCtInfo deviceCtInfo : trackDevices) {
					if (!bottleneckSet && Math.abs(deviceCtInfo.getActualCt() - maxActualCt) < 0.001) {
						deviceCtInfo.setBottleneck(true);
						bottleneckSet = true;
					}
					else {
						deviceCtInfo.setBottleneck(false);
					}
				}
			}
			else {
				// 如果该轨道所有设备CT都为0，则都不是瓶颈工序
				trackDevices.forEach(deviceCtInfo -> deviceCtInfo.setBottleneck(false));
			}
		}
	}

	@Override
	public List<OeeResult> getWorkshopMonthlyOee(String workShopId, AnalyzeQuery query) {
		// 获取车间下所有线体
		var lines = productionLineService.findProdutionLineByWorkshopCode(workShopId);
		// 查询每条线体的OEE
		return lines.stream().map(line -> {
			AnalyzeQuery lineQuery = new AnalyzeQuery();
			lineQuery.setCode(line.getCode());
			lineQuery.setStartTime(query.getStartTime());
			lineQuery.setEndTime(query.getEndTime());
			// 其他必要参数可补充
			return getLineOee(lineQuery);
		}).toList();
	}

}
