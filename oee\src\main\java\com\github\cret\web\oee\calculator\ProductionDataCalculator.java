// package com.github.cret.web.oee.calculator;

// import java.util.ArrayList;
// import java.util.Arrays;
// import java.util.Collections;
// import java.util.List;

// import org.bson.Document;
// import org.bson.conversions.Bson;
// import org.springframework.data.mongodb.core.MongoTemplate;
// import org.springframework.util.StringUtils;

// import com.github.cret.web.oee.domain.analyze.ProductionData;
// import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
// import com.github.cret.web.oee.domain.analyze.query.DeviceQuery;
// import com.github.cret.web.oee.utils.DocumentUtil;
// import com.github.cret.web.oee.utils.OeeUtil;
// import com.mongodb.client.model.Aggregates;
// import com.mongodb.client.model.Filters;
// import com.mongodb.client.model.Projections;
// import com.mongodb.client.model.Sorts;

// /**
// * 生产数据计算器
// */
// @Deprecated
// public class ProductionDataCalculator {

// /**
// * 获取设备生产信息数据
// * @param deviceQuery 设备查询条件
// * @param query 查询条件
// * @param mongoTemplate MongoDB操作模板
// * @return 生产数据列表
// */
// public static List<ProductionData> getDeviceProductionData(DeviceQuery deviceQuery,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {
// return switch (deviceQuery.getType()) {
// case smt_npm_reporter -> getNpmSmtProductionData(deviceQuery.getCode(), query,
// mongoTemplate);
// case smt_npm_reporter2 -> getNpm2SmtProductionData(deviceQuery.getCode(), query,
// mongoTemplate);
// case smt_samsung -> getSamsungSmtProductionData(deviceQuery.getCode(), query,
// mongoTemplate);
// case smt_yamaha -> getYamahaSmtProductionData(deviceQuery.getCode(), query,
// mongoTemplate);
// case aoi_yamaha -> getYamahaAoiProductionData(deviceQuery.getCode(), query,
// mongoTemplate);
// default -> Collections.emptyList();
// };
// }

// public static List<ProductionData> getNpmSmtProductionData(String deviceCode,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {
// // 获取生产日志集合
// String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

// // 构建聚合管道
// List<Document> pipeline = new ArrayList<>();

// // 1. 匹配时间范围和产品型号（如果有）
// Document matchDoc = new Document("time",
// new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

// // 如果指定了产品型号，添加产品型号过滤条件
// if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
// matchDoc.append("productmodel", query.getProductModel());
// }

// pipeline.add(new Document("$match", matchDoc));

// // 2. 按时间排序
// pipeline.add(new Document("$sort", new Document("time", 1)));

// // 3. 添加前一个值字段
// Document setWindowFields = new Document("$setWindowFields",
// new Document().append("sortBy", new Document("time", 1).append("totaltime", -1))
// .append("output",
// new Document()
// .append("previousActualTime",
// new Document("$shift",
// new Document().append("output", "$actualtime").append("by", -1)))
// .append("previousStopTime",
// new Document("$shift",
// new Document().append("output", "$stoptime").append("by", -1)))
// .append("previousTotalTime",
// new Document("$shift",
// new Document().append("output", "$totaltime").append("by", -1)))
// .append("previousBoard",
// new Document("$shift",
// new Document().append("output", "$raw.count.board").append("by", -1)))
// .append("previousModule",
// new Document("$shift",
// new Document().append("output", "$raw.count.module").append("by", -1)))
// .append("previousScstop",
// new Document("$shift",
// new Document().append("output", "$raw.time.scstop").append("by", -1)))
// .append("previousScestop", new Document("$shift",
// new Document().append("output", "$raw.time.scestop").append("by", -1)))));
// pipeline.add(setWindowFields);

// // 4. 添加计算字段
// Document addFields = new Document("$addFields",
// new Document()
// .append("actualTimeResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$actualtime", "$previousActualTime")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$actualtime", "$previousActualTime")))
// .append("else", "$actualtime")))
// .append("stopTimeResult",
// new Document("$cond", new Document()
// .append("if", new Document("$gte", Arrays.asList("$stoptime", "$previousStopTime")))
// .append("then",
// new Document("$subtract", Arrays.asList("$stoptime", "$previousStopTime")))
// .append("else", "$stoptime")))
// .append("totalTimeResult",
// new Document("$cond", new Document()
// .append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousTotalTime")))
// .append("then",
// new Document("$subtract", Arrays.asList("$totaltime", "$previousTotalTime")))
// .append("else", "$totaltime")))
// .append("boardResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.count.board", "$previousBoard")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.count.board", "$previousBoard")))
// .append("else", "$raw.count.board")))
// .append("moduleResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.count.module", "$previousModule")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.count.module", "$previousModule")))
// .append("else", "$raw.count.module")))
// .append("scstopResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.time.scstop", "$previousScstop")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.time.scstop", "$previousScstop")))
// .append("else", "$raw.time.scstop")))
// .append("scestopResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.time.scestop", "$previousScestop")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.time.scestop", "$previousScestop")))
// .append("else", "$raw.time.scestop"))));
// pipeline.add(addFields);

// // 5. 过滤掉第一条记录
// Document filterMatch = new Document("$match",
// new Document().append("actualTimeResult", new Document("$ne", null))
// .append("stopTimeResult", new Document("$ne", null))
// .append("totalTimeResult", new Document("$ne", null))
// .append("boardResult", new Document("$ne", null))
// .append("moduleResult", new Document("$ne", null))
// .append("scstopResult", new Document("$ne", null))
// .append("scestopResult", new Document("$ne", null)));
// pipeline.add(filterMatch);

// // 6. 汇总计算结果
// Document groupDoc = new Document("$group",
// new Document().append("_id", "$productmodel")
// .append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
// .append("stopTimeTotal", new Document("$sum", "$stopTimeResult"))
// .append("totalTimeTotal", new Document("$sum", "$totalTimeResult"))
// .append("boardTotal", new Document("$sum", "$boardResult"))
// .append("moduleTotal", new Document("$sum", "$moduleResult"))
// .append("scstopTotal", new Document("$sum", "$scstopResult"))
// .append("scestopTotal", new Document("$sum", "$scestopResult")));
// pipeline.add(groupDoc);

// // 执行聚合查询
// List<Document> results = mongoTemplate.getCollection(collectionName)
// .aggregate(pipeline)
// .into(new ArrayList<>());

// // 转换结果为ActualRunTime对象列表
// List<ProductionData> list = new ArrayList<>();

// for (Document doc : results) {
// ProductionData productionData = new ProductionData();
// productionData.setDeviceCode(deviceCode);
// productionData.setProductModel1(doc.getString("_id"));
// productionData.setProductModel(doc.getString("_id"));
// productionData.setRunTime(doc.getDouble("actualTimeTotal"));
// productionData.setStopTime(doc.getDouble("stopTimeTotal"));
// productionData.setTotalTime(doc.getDouble("totalTimeTotal"));
// productionData.setBoardNum(doc.getInteger("boardTotal"));
// productionData.setModuleNum(doc.getInteger("moduleTotal"));
// productionData.setScstopTime(doc.getDouble("scstopTotal"));
// productionData.setScestopTime(doc.getDouble("scestopTotal"));
// list.add(productionData);
// }

// return list;

// }

// public static List<ProductionData> getNpm2SmtProductionData(String deviceCode,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {
// // 获取生产日志集合
// String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

// // 构建聚合管道
// List<Document> pipeline = new ArrayList<>();

// // 1. 匹配时间范围和产品型号（如果有）
// Document matchDoc = new Document("time",
// new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

// // 如果指定了产品型号，添加产品型号过滤条件
// if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
// matchDoc.append("productmodel", query.getProductModel());
// }

// pipeline.add(new Document("$match", matchDoc));

// // 2. 按时间排序
// pipeline.add(new Document("$sort", new Document("time", 1)));

// // 3. 添加前一个值字段
// Document setWindowFields = new Document("$setWindowFields",
// new Document().append("sortBy", new Document("time", 1))
// .append("output",
// new Document()
// .append("previousActualTime",
// new Document("$shift",
// new Document().append("output", "$actualtime").append("by", -1)))
// .append("previousStopTime",
// new Document("$shift",
// new Document().append("output", "$stoptime").append("by", -1)))
// .append("previousTotalTime",
// new Document("$shift",
// new Document().append("output", "$totaltime").append("by", -1)))
// .append("previousBoard",
// new Document("$shift",
// new Document().append("output", "$raw.count.board").append("by", -1)))
// .append("previousModule", new Document("$shift",
// new Document().append("output", "$raw.count.module").append("by", -1)))));
// pipeline.add(setWindowFields);

// // 4. 添加计算字段
// Document addFields = new Document("$addFields",
// new Document()
// .append("actualTimeResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$actualtime", "$previousActualTime")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$actualtime", "$previousActualTime")))
// .append("else", "$actualtime")))
// .append("stopTimeResult",
// new Document("$cond", new Document()
// .append("if", new Document("$gte", Arrays.asList("$stoptime", "$previousStopTime")))
// .append("then",
// new Document("$subtract", Arrays.asList("$stoptime", "$previousStopTime")))
// .append("else", "$stoptime")))
// .append("totalTimeResult",
// new Document("$cond", new Document()
// .append("if", new Document("$gte", Arrays.asList("$totaltime", "$previousTotalTime")))
// .append("then",
// new Document("$subtract", Arrays.asList("$totaltime", "$previousTotalTime")))
// .append("else", "$totaltime")))
// .append("boardResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.count.board", "$previousBoard")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.count.board", "$previousBoard")))
// .append("else", "$raw.count.board")))
// .append("moduleResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList("$raw.count.module", "$previousModule")))
// .append("then",
// new Document("$subtract",
// Arrays.asList("$raw.count.module", "$previousModule")))
// .append("else", "$raw.count.module"))));
// pipeline.add(addFields);

// // 5. 过滤掉第一条记录
// Document filterMatch = new Document("$match",
// new Document().append("actualTimeResult", new Document("$ne", null))
// .append("stopTimeResult", new Document("$ne", null))
// .append("totalTimeResult", new Document("$ne", null))
// .append("boardResult", new Document("$ne", null))
// .append("moduleResult", new Document("$ne", null)));
// pipeline.add(filterMatch);

// // 6. 汇总计算结果
// Document groupDoc = new Document("$group",
// new Document().append("_id", "$productmodel")
// .append("actualTimeTotal", new Document("$sum", "$actualTimeResult"))
// .append("stopTimeTotal", new Document("$sum", "$stopTimeResult"))
// .append("totalTimeTotal", new Document("$sum", "$totalTimeResult"))
// .append("boardTotal", new Document("$sum", "$boardResult"))
// .append("moduleTotal", new Document("$sum", "$moduleResult")));
// pipeline.add(groupDoc);

// // 执行聚合查询
// List<Document> results = mongoTemplate.getCollection(collectionName)
// .aggregate(pipeline)
// .into(new ArrayList<>());

// // 转换结果为ActualRunTime对象列表
// List<ProductionData> list = new ArrayList<>();

// for (Document doc : results) {
// ProductionData productionData = new ProductionData();
// productionData.setDeviceCode(deviceCode);
// productionData.setProductModel1(doc.getString("_id"));
// productionData.setProductModel(removeTopOrBotAndAfter(doc.getString("_id")));
// productionData.setRunTime(doc.getDouble("actualTimeTotal"));
// productionData.setStopTime(doc.getDouble("stopTimeTotal"));
// productionData.setTotalTime(doc.getDouble("totalTimeTotal"));
// productionData.setBoardNum(doc.getInteger("boardTotal"));
// productionData.setModuleNum(doc.getInteger("moduleTotal"));
// list.add(productionData);
// }

// return list;

// }

// private static List<ProductionData> getSamsungSmtProductionData(String deviceCode,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {

// // 构建聚合管道
// List<Bson> pipeline = new ArrayList<>();

// // 1. $match 阶段
// List<Bson> matchConditions = new ArrayList<>();
// matchConditions.add(Filters.gte("time", query.getStartTime()));
// matchConditions.add(Filters.lte("time", query.getEndTime()));
// matchConditions.add(Filters.eq("normalflag", true));
// if (StringUtils.hasText(query.getProductModel())) {
// matchConditions.add(Filters.eq("productmodel", query.getProductModel()));
// }
// pipeline.add(Aggregates.match(Filters.and(matchConditions)));

// // 2. $project 阶段
// Document projectStage = new Document("$project",
// new Document().append("productmodel", 1)
// .append("runtime", new Document("$ifNull", Arrays.asList("$samsungindex.runtime", 0)))
// .append("stoptime",
// new Document("$add",
// Arrays.asList(new Document("$ifNull", Arrays.asList("$samsungindex.stoptime", 0)),
// new Document("$ifNull", Arrays.asList("$samsungindex.idletime", 0)))))
// .append("totaltime",
// new Document("$add",
// Arrays.asList(new Document("$ifNull", Arrays.asList("$samsungindex.runtime", 0)),
// new Document("$ifNull", Arrays.asList("$samsungindex.stoptime", 0)),
// new Document("$ifNull", Arrays.asList("$samsungindex.idletime", 0)))))
// .append("moduleNum", new Document("$ifNull", Arrays.asList("$samsungindex.pcbcount",
// 0)))
// .append("boardNum", new Document("$ifNull", Arrays.asList("$samsungindex.panelcount",
// 0))));
// pipeline.add(projectStage);

// // 3. $group 阶段
// Document groupStage = new Document("$group",
// new Document().append("_id", "$productmodel")
// .append("runtime", new Document("$sum", "$runtime"))
// .append("stoptime", new Document("$sum", "$stoptime"))
// .append("totaltime", new Document("$sum", "$totaltime"))
// .append("totalModuleNum", new Document("$sum", "$moduleNum"))
// .append("totalBoardNum", new Document("$sum", "$boardNum")));
// pipeline.add(groupStage);

// // 4. 后处理 $project 阶段
// Document postProjection = new Document("$project", new
// Document().append("productModel", "$_id")
// .append("runtime", 1)
// .append("stoptime", 1)
// .append("totaltime", 1)
// .append("moduleNum", "$totalModuleNum")
// .append("boardNum", "$totalBoardNum")
// .append("flatNum", new Document("$cond", Arrays.asList(
// new Document("$gt", Arrays.asList("$totalBoardNum", 0)),
// new Document("$ceil", new Document("$divide", Arrays.asList("$totalModuleNum",
// "$totalBoardNum"))),
// 0))));
// pipeline.add(postProjection);

// // 5. 排序阶段
// pipeline.add(Aggregates.sort(Sorts.ascending("productModel")));

// // 获取集合名称
// String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

// // 执行聚合查询
// return mongoTemplate.getCollection(collectionName).aggregate(pipeline).map(document ->
// {
// ProductionData data = new ProductionData();
// data.setDeviceCode(deviceCode);
// data.setProductModel1(document.getString("productModel"));
// data.setProductModel(extractBase(document.getString("productModel")));
// data.setRunTime(Double.valueOf(document.getInteger("runtime")));
// data.setStopTime(Double.valueOf(document.getInteger("stoptime")));
// data.setTotalTime(Double.valueOf(document.getInteger("totaltime")));
// data.setModuleNum(document.getInteger("moduleNum"));
// data.setBoardNum(document.getInteger("boardNum"));
// data.setFlatNum(document.getDouble("flatNum").intValue());
// return data;
// }).into(new ArrayList<>());
// }

// private static List<ProductionData> getYamahaSmtProductionData(String deviceCode,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {

// List<Bson> pipeline = new ArrayList<>();

// List<Bson> matchConditions = new ArrayList<>();
// matchConditions.add(Filters.gte("time", query.getStartTime()));
// matchConditions.add(Filters.lte("time", query.getEndTime()));
// if (StringUtils.hasText(query.getProductModel())) {
// matchConditions.add(Filters.eq("productmodel", query.getProductModel()));
// }
// pipeline.add(Aggregates.match(Filters.and(matchConditions)));

// // 1. $sort阶段：按时间升序排序
// pipeline.add(Aggregates.sort(Sorts.ascending("time")));

// // 2. 第一个$setWindowFields阶段：获取前一个文档的productmodel
// Document setWindowFields1 = new Document("$setWindowFields",
// new Document().append("partitionBy", null)
// .append("sortBy", new Document("time", 1))
// .append("output", new Document("prevProduct", new Document("$shift",
// new Document("output", "$productmodel").append("by", -1).append("default", null)))));
// pipeline.add(setWindowFields1);

// // 3. 第二个$setWindowFields阶段：生成groupId
// Document setWindowFields2 = new Document("$setWindowFields", new
// Document().append("partitionBy", null)
// .append("sortBy", new Document("time", 1))
// .append("output", new Document("groupId", new Document("$sum",
// new Document("$cond",
// Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevProduct")), 1,
// 0)))
// .append("window", new Document("documents", Arrays.asList("unbounded", "current"))))));
// pipeline.add(setWindowFields2);

// // 4. $project阶段：移除prevProduct字段
// pipeline.add(Aggregates.project(Projections.exclude("prevProduct")));

// // 5. $addFields阶段：计算运行和停止时间总和
// Document addFields = new Document("$addFields",
// new Document()
// .append("addruntime",
// new Document("$add",
// Arrays.asList("$mountingcta", "$mountingctb", "$mountingctc", "$mountingctd",
// "$transferct", "$markrecognitioncta", "$markrecognitionctb",
// "$markrecognitionctc", "$markrecognitionctd")))
// .append("addstoptime",
// new Document("$add", Arrays.asList("$standbyct", "$errorshutdowntime",
// "$errorrecoverytime",
// "$awaitingothertracktime", "$operatorshutdowntime", "$otherconveyingtabletime"))));
// pipeline.add(addFields);

// // 6. 第一个$group阶段：按复合键分组并聚合
// Document group1 = new Document("$group",
// new Document("_id",
// new Document().append("productmodel", "$productmodel")
// .append("groupId", "$groupId")
// .append("batchsequencenumber", "$batchsequencenumber"))
// .append("runtime", new Document("$max", "$addruntime"))
// .append("stoptime", new Document("$max", "$addstoptime"))
// .append("completedpanelcount", new Document("$last", "$completedpanelcount")));
// pipeline.add(group1);

// // 7. 第二个$group阶段：按productmodel和groupId分组并统计
// Document group2 = new Document("$group",
// new Document("_id",
// new Document().append("productmodel", "$_id.productmodel").append("result",
// "$_id.groupId"))
// .append("actualTimeTotal", new Document("$sum", "$runtime"))
// .append("stopTimeTotal", new Document("$sum", "$stoptime"))
// .append("boardTotal", new Document("$sum", 1))
// .append("moduleTotal", new Document("$sum", "$completedpanelcount"))
// .append("flatnum", new Document("$first", "$completedpanelcount")));
// pipeline.add(group2);

// // 执行聚合查询
// String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

// List<Document> results = mongoTemplate.getCollection(collectionName)
// .aggregate(pipeline)
// .into(new ArrayList<>());

// // 转换结果为ActualRunTime对象列表
// List<ProductionData> list = new ArrayList<>();

// for (Document doc : results) {
// ProductionData data = new ProductionData();
// // 根据实际返回的文档结构设置字段
// Document id = doc.get("_id", Document.class);
// data.setProductModel1(id.getString("productmodel"));
// data.setProductModel(extractBase(id.getString("productmodel")));
// data.setRunTime(DocumentUtil.getDouble(doc.get("actualTimeTotal")));
// data.setStopTime(DocumentUtil.getDouble(doc.get("stopTimeTotal")));
// data.setBoardNum(doc.getInteger("boardTotal"));
// data.setModuleNum(doc.getInteger("moduleTotal"));
// data.setFlatNum(doc.getInteger("flatnum"));

// list.add(data);
// }

// return list;
// }

// public static List<ProductionData> getYamahaAoiProductionData(String deviceCode,
// AnalyzeQuery query,
// MongoTemplate mongoTemplate) {
// // Get production log collection
// String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

// // Build aggregation pipeline
// List<Document> pipeline = new ArrayList<>();

// // 1. Match time range and product model (if specified)
// Document matchDoc = new Document("time",
// new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

// if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
// matchDoc.append("productmodel", query.getProductModel());
// }
// pipeline.add(new Document("$match", matchDoc));

// // 2. Sort by time
// pipeline.add(new Document("$sort", new Document("time", 1)));

// // 3. Add previous value fields using window functions
// Document setWindowFields = new Document("$setWindowFields",
// new Document().append("sortBy", new Document("time", 1))
// .append("output", new Document().append("previousTime",
// new Document("$shift", new Document().append("output", "$time").append("by", -1)))));
// pipeline.add(setWindowFields);

// // 4. Calculate time differences and count differences
// Document addFields = new Document("$addFields",
// new Document()
// .append("timeResult",
// new Document("$cond",
// new Document()
// .append("if",
// new Document("$gte",
// Arrays.asList(new Document("$toLong", "$time"),
// new Document("$toLong", "$previousTime"))))
// .append("then",
// new Document("$divide", Arrays.asList(
// new Document("$subtract",
// Arrays.asList(new Document("$toLong", "$time"),
// new Document("$toLong", "$previousTime"))),
// 1000)))
// .append("else", 0))));
// pipeline.add(addFields);

// // 5. Filter out null results
// pipeline.add(new Document("$match", new Document().append("timeResult", new
// Document("$ne", null))));

// // 6. Group by product model
// pipeline.add(new Document("$group",
// new Document().append("_id", "$productmodel")
// .append("totalTime", new Document("$sum", "$timeResult"))
// .append("totalCount", new Document("$sum", 1))));

// // Execute aggregation
// List<Document> results = mongoTemplate.getCollection(collectionName)
// .aggregate(pipeline)
// .into(new ArrayList<>());

// // Convert results to ProductionData objects
// List<ProductionData> list = new ArrayList<>();
// for (Document doc : results) {
// ProductionData productionData = new ProductionData();
// productionData.setDeviceCode(deviceCode);
// productionData.setProductModel1(doc.getString("_id"));
// productionData.setProductModel(removeTopOrBotAndAfter(doc.getString("_id")));
// productionData.setRunTime(doc.getDouble("totalTime"));
// productionData.setTotalTime(doc.getDouble("totalTime"));
// productionData.setBoardNum(doc.getInteger("totalCount"));
// list.add(productionData);
// }

// return list;
// }

// /**
// * 移除TOP或者BOT
// */
// public static String removeTopOrBotAndAfter(String input) {
// // 找到"TOP"或"BOT"的位置
// int topIndex = input.indexOf("TOP");
// int botIndex = input.indexOf("BOT");

// // 确定是"TOP"还是"BOT"
// int targetIndex = -1;
// if (topIndex != -1) {
// targetIndex = topIndex;
// }
// else if (botIndex != -1) {
// targetIndex = botIndex;
// }

// // 如果找到"TOP"或"BOT"，则截取前面的部分
// if (targetIndex != -1) {
// return input.substring(0, targetIndex - 1); // 减去1以去除前面的"-"
// }

// // 如果没有找到"TOP"或"BOT"，返回原字符串
// return input;
// }

// // private static String extractBase(String input) {
// // int lastDashIndex = input.lastIndexOf('-');
// // if (lastDashIndex != -1 && lastDashIndex < input.length() - 1) {
// // // 获取短横线后第一个字符
// // char targetChar = input.charAt(lastDashIndex + 1);
// // // 拼接基础部分和首字母
// // return input.substring(0, lastDashIndex + 1) + targetChar;
// // }
// // return input; // 格式不符时返回原字符串
// // }

// private static String extractBase(String input) {
// int lastDashIndex = input.lastIndexOf('-');
// if (lastDashIndex != -1 && lastDashIndex < input.length() - 1) {
// String prefix = input.substring(0, lastDashIndex + 1);
// String suffix = input.substring(lastDashIndex + 1);
// String trimmedSuffix = trimTrailingDigits(suffix);
// return prefix + trimmedSuffix;
// }
// return input; // 格式不符时返回原字符串
// }

// private static String trimTrailingDigits(String s) {
// if (s.isEmpty()) {
// return s;
// }
// int i = s.length() - 1;
// while (i >= 0 && Character.isDigit(s.charAt(i))) {
// i--;
// }
// return s.substring(0, i + 1);
// }

// }
