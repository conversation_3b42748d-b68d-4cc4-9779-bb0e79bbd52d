package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolutionSend;

@Repository
public interface FeedbackTriggerSolutionSendRepository extends MongoRepository<FeedbackTriggerSolutionSend, String> {

	/**
	 * 根据解决方案ID查找发送记录
	 * @param triggerSolutionId 解决方案ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByTriggerSolutionId(String triggerSolutionId);

	/**
	 * 根据发送状态查找发送记录
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findBySendStatus(Boolean sendStatus);

	/**
	 * 根据解决方案ID和发送状态查找发送记录
	 * @param triggerSolutionId 解决方案ID
	 * @param sendStatus 发送状态
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByTriggerSolutionIdAndSendStatus(String triggerSolutionId,
			Boolean sendStatus);

	/**
	 * 查找预期发送时间在指定时间之前且未发送的记录
	 * @param expectedSendTime 预期发送时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByExpectedSendTimeBeforeAndSendStatusFalse(Date expectedSendTime);

	/**
	 * 查找需要发送的记录：预期发送时间小于等于当前时间且发送状态为false的记录
	 * @param currentTime 当前时间
	 * @param sendStatus 发送状态
	 * @return 需要发送的记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByExpectedSendTimeLessThanEqualAndSendStatus(Date currentTime,
			Boolean sendStatus);

	/**
	 * 查找预期发送时间在指定时间范围内的记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByExpectedSendTimeBetween(Date startTime, Date endTime);

	/**
	 * 根据发送人用户ID查找发送记录
	 * @param userId 用户ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findBySendUserId_NoticeUserId(String userId);

	/**
	 * 根据告知人查找发送记录
	 * @param userId 用户ID
	 * @return 发送记录列表
	 */
	List<FeedbackTriggerSolutionSend> findByReportUserIdContaining(String userId);

}
