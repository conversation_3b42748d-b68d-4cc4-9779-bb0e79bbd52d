package com.github.cret.web.oee.domain.analyze.query;

import com.github.cret.web.oee.enums.DeviceType;

public class DeviceQuery {

	String code;

	DeviceType type;

	String productModel;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public DeviceType getType() {
		return type;
	}

	public void setType(DeviceType type) {
		this.type = type;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

}
