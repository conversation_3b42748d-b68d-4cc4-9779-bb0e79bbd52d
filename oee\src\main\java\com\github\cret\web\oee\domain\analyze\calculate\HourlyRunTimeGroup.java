package com.github.cret.web.oee.domain.analyze.calculate;

import java.util.Date;

/**
 * 每小时运行时间分组（按小时和产品型号分组）
 */
public class HourlyRunTimeGroup {

	// 小时
	private String hour;

	// 产品型号
	private String productModel;

	// 运行时间
	private Double runTime;

	private Date firstTime;

	public String getHour() {
		return hour;
	}

	public void setHour(String hour) {
		this.hour = hour;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Double getRunTime() {
		return runTime;
	}

	public void setRunTime(Double runTime) {
		this.runTime = runTime;
	}

	public Date getFirstTime() {
		return firstTime;
	}

	public void setFirstTime(Date firstTime) {
		this.firstTime = firstTime;
	}

}
