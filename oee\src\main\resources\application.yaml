---
server:
  port: 8091

spring:
  profiles:
    active: dev
  jackson:
    default-property-inclusion: NON_NULL
  session:
    timeout: 30s
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
  web:
    resources:
      add-mappings: false

proxy:
  server-url: http://************:8092

feedback:
  handleBaseUrl: http://localhost:3333/feedback/handle
  dashboardBaseUrl: http://************:3000/dashboard
  defaultDashboardPath: SMT1-5