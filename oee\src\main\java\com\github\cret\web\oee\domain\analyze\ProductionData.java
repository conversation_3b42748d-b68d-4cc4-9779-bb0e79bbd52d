package com.github.cret.web.oee.domain.analyze;

import com.github.cret.web.oee.enums.DeviceCategory;

/**
 * 生产数据实体 - 记录设备生产过程中的各项指标数据
 */
public class ProductionData {

	/* 产品信息 */
	/** 当前产品型号 */
	private String productModel;

	/** 原始产品型号（变更前） */
	private String originalProductModel;

	/** 生产批次ID */
	private Integer groupId;

	/* 设备信息 */
	/** 设备编码 */
	private String deviceCode;

	/** 设备类型 */
	private DeviceCategory deviceCategory;

	/** 轨道类型 */
	private String trackType;

	/* 时间指标（单位：秒） */
	/** 设备运行时长 */
	private Double runTime;

	/** 设备停止时长 */
	private Double stopTime;

	/** 总运行时长 */
	private Double totalTime;

	/** 软件控制停机时长 */
	private Double scStopTime;

	/** 软件紧急停机时长 */
	private Double scEmergencyStopTime;

	/* 生产数量 */
	/** 拼板生产数量 */
	private Integer moduleNum;

	/** 单板生产数量 */
	private Integer boardNum;

	/** 拼板数量 */
	private Integer flatNum;

	/* 效率指标 */
	/** 理论生产周期（秒/单位） */
	private Double theoreticalCt;

	/** 实际生产周期（秒/单位） */
	private Double actualCt;

	/** 是否瓶颈设备 */
	private Boolean bottleneck;

	/* 分组信息 */
	/** 设备分组编号 */
	private Integer deviceGroup;

	/* Getter/Setter 方法 */
	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public String getOriginalProductModel() {
		return originalProductModel;
	}

	public void setOriginalProductModel(String originalProductModel) {
		this.originalProductModel = originalProductModel;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public DeviceCategory getDeviceCategory() {
		return deviceCategory;
	}

	public void setDeviceCategory(DeviceCategory deviceCategory) {
		this.deviceCategory = deviceCategory;
	}

	public String getTrackType() {
		return trackType;
	}

	public void setTrackType(String trackType) {
		this.trackType = trackType;
	}

	public Double getRunTime() {
		return runTime;
	}

	public void setRunTime(Double runTime) {
		this.runTime = runTime;
	}

	public Double getStopTime() {
		return stopTime;
	}

	public void setStopTime(Double stopTime) {
		this.stopTime = stopTime;
	}

	public Double getTotalTime() {
		return totalTime;
	}

	public void setTotalTime(Double totalTime) {
		this.totalTime = totalTime;
	}

	public Double getScStopTime() {
		return scStopTime;
	}

	public void setScStopTime(Double scStopTime) {
		this.scStopTime = scStopTime;
	}

	public Double getScEmergencyStopTime() {
		return scEmergencyStopTime;
	}

	public void setScEmergencyStopTime(Double scEmergencyStopTime) {
		this.scEmergencyStopTime = scEmergencyStopTime;
	}

	public Integer getModuleNum() {
		return moduleNum;
	}

	public void setModuleNum(Integer moduleNum) {
		this.moduleNum = moduleNum;
	}

	public Integer getBoardNum() {
		return boardNum;
	}

	public void setBoardNum(Integer boardNum) {
		this.boardNum = boardNum;
	}

	public Integer getFlatNum() {
		return flatNum;
	}

	public void setFlatNum(Integer flatNum) {
		this.flatNum = flatNum;
	}

	public Double getTheoreticalCt() {
		return theoreticalCt;
	}

	public void setTheoreticalCt(Double theoreticalCt) {
		this.theoreticalCt = theoreticalCt;
	}

	public Double getActualCt() {
		return actualCt;
	}

	public void setActualCt(Double actualCt) {
		this.actualCt = actualCt;
	}

	public Boolean getBottleneck() {
		return bottleneck;
	}

	public void setBottleneck(Boolean bottleneck) {
		this.bottleneck = bottleneck;
	}

	public Integer getDeviceGroup() {
		return deviceGroup;
	}

	public void setDeviceGroup(Integer deviceGroup) {
		this.deviceGroup = deviceGroup;
	}

	/**
	 * 计算实际生产周期 逻辑：实际CT = 运行时间 / 生产数量（处理除零情况）
	 */
	public void calculateActualCt() {
		if (boardNum != null && boardNum != 0 && runTime != null) {
			double rawCt = runTime / boardNum;
			this.actualCt = Math.round(rawCt * 100) / 100.0; // 保留两位小数
		}
		else {
			this.actualCt = 0.0;
		}
	}

	/* 参数校验方法示例 */
	public void validateTimeMetrics() {
		if (runTime < 0 || stopTime < 0) {
			throw new IllegalArgumentException("时间指标不能为负数");
		}
	}

	/**
	 * 计算拼板数量（向上取整） 逻辑：拼板数 = ⌈总生产数量 / 大板数量⌉ 要求：当两个值均存在且均为正数时计算，否则置零
	 */
	public void calculateFlatNumber() {
		if (moduleNum != null && moduleNum > 0 && boardNum != null && boardNum > 0) {
			// 转换为double确保精确除法计算
			double divisionResult = (double) moduleNum / boardNum;
			// 使用Math.ceil实现向上取整
			this.flatNum = (int) Math.ceil(divisionResult);
		}
		else {
			this.flatNum = 0;
		}
	}

}