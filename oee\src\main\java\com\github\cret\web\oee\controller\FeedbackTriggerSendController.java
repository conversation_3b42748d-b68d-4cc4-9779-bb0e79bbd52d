package com.github.cret.web.oee.controller;

import java.util.Date;
import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.annotation.OpLog;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;

@RestController
@RequestMapping("/feedback-trigger-send")
public class FeedbackTriggerSendController {

	private final FeedbackTriggerSendService feedbackTriggerSendService;

	public FeedbackTriggerSendController(FeedbackTriggerSendService feedbackTriggerSendService) {
		this.feedbackTriggerSendService = feedbackTriggerSendService;
	}

	// ==================== 基础CRUD操作 ====================

	@PostMapping
	@OpLog(name = "创建反馈触发发送记录")
	public FeedbackTriggerSend create(@RequestBody FeedbackTriggerSend feedbackTriggerSend) {
		return feedbackTriggerSendService.save(feedbackTriggerSend);
	}

	@GetMapping("/{id}")
	public FeedbackTriggerSend getById(@PathVariable String id) {
		return feedbackTriggerSendService.findById(id);
	}

	@PostMapping("/page")
	public PageList<FeedbackTriggerSend> page(@RequestBody PageableParam<FeedbackTriggerSend> param) {
		return feedbackTriggerSendService.page(param);
	}

	@PostMapping("/list")
	public List<FeedbackTriggerSend> list(@RequestBody FeedbackTriggerSend param) {
		return feedbackTriggerSendService.findList(param);
	}

	@GetMapping
	public List<FeedbackTriggerSend> getAll() {
		return feedbackTriggerSendService.findAll();
	}

	@PutMapping("/{id}")
	@OpLog(name = "更新反馈触发发送记录")
	public FeedbackTriggerSend update(@PathVariable String id, @RequestBody FeedbackTriggerSend feedbackTriggerSend) {
		feedbackTriggerSend.setId(id);
		return feedbackTriggerSendService.save(feedbackTriggerSend);
	}

	@DeleteMapping("/{id}")
	@OpLog(name = "删除反馈触发发送记录")
	public void delete(@PathVariable String id) {
		feedbackTriggerSendService.deleteById(id);
	}

	@DeleteMapping("/batch")
	@OpLog(name = "批量删除反馈触发发送记录")
	public void batchDelete(@RequestBody List<String> ids) {
		feedbackTriggerSendService.batchDelete(ids);
	}

	// ==================== 业务查询接口 ====================

	@GetMapping("/trigger-record/{triggerRecordId}")
	public List<FeedbackTriggerSend> getByTriggerRecordId(@PathVariable String triggerRecordId) {
		return feedbackTriggerSendService.findByTriggerRecordId(triggerRecordId);
	}

	@GetMapping("/send-status/{sendStatus}")
	public List<FeedbackTriggerSend> getBySendStatus(@PathVariable Boolean sendStatus) {
		return feedbackTriggerSendService.findBySendStatus(sendStatus);
	}

	@GetMapping("/trigger-record/{triggerRecordId}/send-status/{sendStatus}")
	public List<FeedbackTriggerSend> getByTriggerRecordIdAndSendStatus(@PathVariable String triggerRecordId,
			@PathVariable Boolean sendStatus) {
		return feedbackTriggerSendService.findByTriggerRecordIdAndSendStatus(triggerRecordId, sendStatus);
	}

	@GetMapping("/pending")
	public List<FeedbackTriggerSend> getPendingSendRecords(@RequestParam Date expectedSendTime) {
		return feedbackTriggerSendService.findPendingSendRecords(expectedSendTime);
	}

	@GetMapping("/expected-send-time-between")
	public List<FeedbackTriggerSend> getByExpectedSendTimeBetween(@RequestParam Date startTime,
			@RequestParam Date endTime) {
		return feedbackTriggerSendService.findByExpectedSendTimeBetween(startTime, endTime);
	}

	// ==================== 状态更新接口 ====================

	@PutMapping("/{id}/send-status")
	@OpLog(name = "更新发送状态")
	public FeedbackTriggerSend updateSendStatus(@PathVariable String id, @RequestParam Boolean sendStatus,
			@RequestParam(required = false) String sendResult) {
		return feedbackTriggerSendService.updateSendStatus(id, sendStatus, sendResult);
	}

	@PutMapping("/{id}/mark-sent")
	@OpLog(name = "标记为已发送")
	public FeedbackTriggerSend markAsSent(@PathVariable String id, @RequestParam(required = false) String sendResult) {
		return feedbackTriggerSendService.markAsSent(id, sendResult);
	}

	@PutMapping("/{id}/mark-failed")
	@OpLog(name = "标记为发送失败")
	public FeedbackTriggerSend markAsFailed(@PathVariable String id,
			@RequestParam(required = false) String sendResult) {
		return feedbackTriggerSendService.markAsFailed(id, sendResult);
	}

	// ==================== 业务操作接口 ====================

	@PostMapping("/{id}/perform-send")
	@OpLog(name = "执行发送操作")
	public String performSend(@PathVariable String id) {
		FeedbackTriggerSend sendRecord = feedbackTriggerSendService.findById(id);
		return feedbackTriggerSendService.send(sendRecord);
	}

	@PostMapping("/process-scheduled-sends")
	@OpLog(name = "处理定时发送任务")
	public void processScheduledSends() {
		feedbackTriggerSendService.processScheduledSends();
	}

}
