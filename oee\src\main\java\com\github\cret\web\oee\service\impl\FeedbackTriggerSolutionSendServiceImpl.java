package com.github.cret.web.oee.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.common.util.JacksonUtil;
import com.github.cret.web.oee.config.FeedbackConfig;
import com.github.cret.web.oee.config.WxWorkConfig;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolution;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSolutionSend;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.domain.request.FeedbackUrlParams;
import com.github.cret.web.oee.domain.wxwork.MsgRes;
import com.github.cret.web.oee.domain.wxwork.R;
import com.github.cret.web.oee.domain.wxwork.TextCard;
import com.github.cret.web.oee.domain.wxwork.TextCardContent;
import com.github.cret.web.oee.repository.FeedbackTriggerSolutionSendRepository;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.oee.service.FeedbackTriggerSolutionSendService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.WxWorkMessageUtil;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class FeedbackTriggerSolutionSendServiceImpl implements FeedbackTriggerSolutionSendService {

	private static final Logger logger = LoggerFactory.getLogger(FeedbackTriggerSolutionSendServiceImpl.class);

	private final FeedbackTriggerSolutionSendRepository repository;

	private final MongoTemplate mongoTemplate;

	private final RestTemplate restTemplate;

	private final WxWorkConfig wxWorkConfig;

	private final FeedbackConfig feedbackConfig;

	private final FeedbackTriggerRecordService feedbackTriggerRecordService;

	public FeedbackTriggerSolutionSendServiceImpl(FeedbackTriggerSolutionSendRepository repository,
			MongoTemplate mongoTemplate, RestTemplate restTemplate, WxWorkConfig wxWorkConfig,
			FeedbackConfig feedbackConfig, FeedbackTriggerRecordService feedbackTriggerRecordService) {
		this.repository = repository;
		this.mongoTemplate = mongoTemplate;
		this.restTemplate = restTemplate;
		this.wxWorkConfig = wxWorkConfig;
		this.feedbackConfig = feedbackConfig;
		this.feedbackTriggerRecordService = feedbackTriggerRecordService;
	}

	@Override
	@Transactional
	public FeedbackTriggerSolutionSend save(FeedbackTriggerSolutionSend solutionSend) {
		AuthUser authUser = SecurityUtil.getCurrentUser();
		Date now = new Date();

		if (solutionSend.getId() == null) {
			// 新建记录
			solutionSend.setCreateTime(now);
			solutionSend.setCreateBy(authUser != null ? authUser.id() : null);
		}

		// 设置更新信息
		solutionSend.setUpdateTime(now);
		solutionSend.setUpdateBy(authUser != null ? authUser.id() : null);

		return repository.save(solutionSend);
	}

	@Override
	public FeedbackTriggerSolutionSend findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerSolutionSend> page(PageableParam<FeedbackTriggerSolutionSend> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerSolutionId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains())
			.withIgnorePaths("_class");
		Example<FeedbackTriggerSolutionSend> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findList(FeedbackTriggerSolutionSend param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerSolutionId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains());
		Example<FeedbackTriggerSolutionSend> example = Example.of(param, matcher);
		return repository.findAll(example);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findAll() {
		return mongoTemplate.findAll(FeedbackTriggerSolutionSend.class);
	}

	@Override
	public void deleteById(String id) {
		FeedbackTriggerSolutionSend record = findById(id);
		if (record != null) {
			repository.deleteById(id);
		}
	}

	@Override
	@Transactional
	public void batchDelete(List<String> ids) {
		if (ids != null && !ids.isEmpty()) {
			repository.deleteAllById(ids);
		}
	}

	// ==================== 业务查询方法 ====================

	@Override
	public List<FeedbackTriggerSolutionSend> findByTriggerSolutionId(String triggerSolutionId) {
		return repository.findByTriggerSolutionId(triggerSolutionId);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findBySendStatus(Boolean sendStatus) {
		return repository.findBySendStatus(sendStatus);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findByTriggerSolutionIdAndSendStatus(String triggerSolutionId,
			Boolean sendStatus) {
		return repository.findByTriggerSolutionIdAndSendStatus(triggerSolutionId, sendStatus);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findPendingSendRecords(Date expectedSendTime) {
		return repository.findByExpectedSendTimeBeforeAndSendStatusFalse(expectedSendTime);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findByExpectedSendTimeBetween(Date startTime, Date endTime) {
		return repository.findByExpectedSendTimeBetween(startTime, endTime);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findBySendUserId(String userId) {
		return repository.findBySendUserIdContaining(userId);
	}

	@Override
	public List<FeedbackTriggerSolutionSend> findByReportUserId(String userId) {
		return repository.findByReportUserIdContaining(userId);
	}

	// ==================== 状态更新方法 ====================

	@Override
	@Transactional
	public FeedbackTriggerSolutionSend updateSendStatus(String id, Boolean sendStatus, String sendResult) {
		FeedbackTriggerSolutionSend record = findById(id);
		record.setSendStatus(sendStatus);
		record.setSendResult(sendResult);
		if (sendStatus != null && sendStatus) {
			record.setSendTime(new Date());
		}
		return repository.save(record);
	}

	@Override
	@Transactional
	public FeedbackTriggerSolutionSend markAsSent(String id, String sendResult) {
		return updateSendStatus(id, true, sendResult);
	}

	@Override
	@Transactional
	public FeedbackTriggerSolutionSend markAsFailed(String id, String sendResult) {
		return updateSendStatus(id, false, sendResult);
	}

	@Override
	public String performSend(FeedbackTriggerSolutionSend sendRecord) {
		// 1. 参数验证
		if (sendRecord == null) {
			logger.error("发送记录不能为空");
			return "发送失败：发送记录不能为空";
		}

		String sendRecordId = sendRecord.getId();

		// 2. 验证接收人
		List<String> sendUserId = sendRecord.getSendUserId();
		if (sendUserId == null || sendUserId.isEmpty()) {
			logger.warn("解决方案通知无有效接收人，记录ID: {}", sendRecordId);
			return "发送跳过：无有效接收人";
		}

		// 3. 构建企业微信消息内容
		TextCardContent textCardContent = new TextCardContent();
		textCardContent.setTitle("解决方案通知");
		textCardContent.setDescription(sendRecord.getSendInfo());

		// 构建包含用户信息的个性化URL
		FeedbackUrlParams feedbackUrlParams =
		BuilderUtil.builder(FeedbackUrlParams::new)
		.with(FeedbackUrlParams::setAction, "colse")
		.with(FeedbackUrlParams::setTriggerRecordId, sendRecord.getTriggerRecordId())
		.with(FeedbackUrlParams::setSendRecordId, sendRecord.getTriggerSendId())
		.with(FeedbackUrlParams::setUserId, userId)
		.with(FeedbackUrlParams::setUserName, userName)
		.build();
		// String personalizedUrl = feedbackConfig.buildFeedbackUrl(feedbackUrlParams);
		// textCardContent.setUrl(
		// feedbackConfig.buildFeedbackCloseUrl(sendRecord.getTriggerRecordId(),
		// sendRecord.getTriggerSendId()));
		textCardContent.setBtntxt("查看详情");

		String toUser = sendUserId.stream().collect(Collectors.joining("|"));
		TextCard textCard = new TextCard(toUser, null, null, "textcard", textCardContent,
				wxWorkConfig.getEnableIdTrans(), wxWorkConfig);

		String msg = JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(textCard));
		if (msg == null) {
			logger.error("消息序列化失败，记录ID: {}", sendRecordId);
			return "发送失败：消息格式化错误";
		}

		// 4. 发送企业微信消息
		R<MsgRes> sendResult = WxWorkMessageUtil.sendWxWorkMsg(msg, wxWorkConfig, restTemplate);

		// 5. 处理发送结果并返回详细信息
		return buildSolutionSendResult(sendRecord, sendResult);
	}

	/**
	 * 构建解决方案发送结果信息
	 * @param sendRecord 发送记录
	 * @param sendResult 企业微信发送结果
	 * @return 发送结果描述
	 */
	private String buildSolutionSendResult(FeedbackTriggerSolutionSend sendRecord, R<MsgRes> sendResult) {
		String sendRecordId = sendRecord.getId();

		// 计算目标接收人数
		int targetRecipients = 0;
		if (sendRecord.getSendUserId() != null) {
			targetRecipients += sendRecord.getSendUserId().size();
		}
		if (sendRecord.getReportUserId() != null) {
			targetRecipients += sendRecord.getReportUserId().size();
		}

		// 检查发送结果
		if (sendResult == null) {
			logger.error("企业微信发送结果为空，记录ID: {}", sendRecordId);
			return String.format("发送失败：未收到响应，目标接收人数: %d", targetRecipients);
		}

		if (sendResult.isFail()) {
			logger.warn("企业微信消息发送失败，记录ID: {}, 错误: {}", sendRecordId, sendResult.getMsg());
			return String.format("发送失败：%s，目标接收人数: %d", sendResult.getMsg(), targetRecipients);
		}

		// 发送成功，分析具体结果
		MsgRes msgRes = sendResult.getData();
		if (msgRes == null) {
			logger.warn("企业微信响应数据为空，记录ID: {}", sendRecordId);
			return String.format("发送状态未知：响应数据为空，目标接收人数: %d", targetRecipients);
		}

		// 检查企业微信错误码
		Integer errCode = msgRes.getErrCode();
		if (errCode == null || errCode != 0) {
			String errMsg = msgRes.getErrMsg() != null ? msgRes.getErrMsg() : "未知错误";
			logger.warn("企业微信返回错误，记录ID: {}, 错误码: {}, 错误信息: {}", sendRecordId, errCode, errMsg);
			return String.format("发送失败：%s (错误码: %s)，目标接收人数: %d", errMsg, errCode, targetRecipients);
		}

		// 分析无效用户
		List<String> invalidUsers = msgRes.getInvalidUserList();
		int successfulRecipients = targetRecipients - (invalidUsers != null ? invalidUsers.size() : 0);

		StringBuilder resultBuilder = new StringBuilder();
		if (successfulRecipients > 0) {
			resultBuilder.append(String.format("发送成功，实际通知 %d 人", successfulRecipients));
			logger.info("解决方案通知发送成功，记录ID: {}, 实际通知人数: {}, 目标人数: {}", sendRecordId, successfulRecipients,
					targetRecipients);
		}
		else {
			resultBuilder.append("发送失败，无有效接收人");
			logger.warn("解决方案通知发送失败，记录ID: {}, 所有接收人均无效", sendRecordId);
		}

		// 添加无效用户信息
		if (invalidUsers != null && !invalidUsers.isEmpty()) {
			resultBuilder.append(String.format("，无效用户: %s", String.join(",", invalidUsers)));
			logger.warn("解决方案通知存在无效用户，记录ID: {}, 无效用户: {}", sendRecordId, invalidUsers);
		}

		// 添加消息ID信息
		String msgId = msgRes.getMsgId();
		if (msgId != null) {
			resultBuilder.append(String.format("，消息ID: %s", msgId));
		}

		return resultBuilder.toString();
	}

	@Override
	public void processScheduledSends() {
		Date currentTime = new Date();

		// 查找所有到期且未发送的记录
		List<FeedbackTriggerSolutionSend> pendingSends = repository
			.findByExpectedSendTimeLessThanEqualAndSendStatus(currentTime, false);

		if (pendingSends.isEmpty()) {
			logger.debug("没有需要发送的解决方案通知记录");
			return;
		}

		logger.info("开始处理解决方案通知定时发送任务，共找到 {} 条待发送记录", pendingSends.size());

		int successCount = 0;
		int failureCount = 0;

		for (FeedbackTriggerSolutionSend sendRecord : pendingSends) {
			try {
				// 执行发送
				String sendResult = performSend(sendRecord);

				// 更新发送状态
				sendRecord.setSendStatus(true);
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult(sendResult);

				// 保存更新后的记录
				repository.save(sendRecord);

				successCount++;
				logger.debug("发送成功，记录ID: {}, 结果: {}", sendRecord.getId(), sendResult);

			}
			catch (Exception e) {
				// 发送失败，记录错误信息但不影响其他记录的处理
				sendRecord.setSendStatus(true); // 标记为已处理，避免重复发送
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult("发送失败: " + e.getMessage());

				repository.save(sendRecord);

				failureCount++;
				logger.error("发送失败，记录ID: {}, 错误: {}", sendRecord.getId(), e.getMessage(), e);
			}
		}

		logger.info("解决方案通知定时发送任务完成，成功: {} 条，失败: {} 条", successCount, failureCount);
	}

	@Override
	@Transactional
	public FeedbackTriggerSolutionSend createSolutionNotification(FeedbackTriggerSolution savedSolution,
			List<NoticeUser> noticeUsers) {
		FeedbackTriggerSolutionSend solutionSend = new FeedbackTriggerSolutionSend();

		// 设置解决方案ID
		solutionSend.setTriggerSolutionId(savedSolution.getId());

		// 设置触发记录ID和发送记录ID
		solutionSend.setTriggerRecordId(savedSolution.getTriggerRecordId());
		solutionSend.setTriggerSendId(savedSolution.getTriggerSendId());

		// 从NoticeUsers中提取用户ID列表
		List<String> userIds = new ArrayList<>();
		if (noticeUsers != null && !noticeUsers.isEmpty()) {
			for (NoticeUser noticeUser : noticeUsers) {
				if (noticeUser.getNoticeUserId() != null && !noticeUser.getNoticeUserId().trim().isEmpty()) {
					userIds.add(noticeUser.getNoticeUserId());
				}
			}
		}

		// 设置发送人为通知用户列表
		solutionSend.setSendUserId(userIds);

		// 构建包含异常记录信息的发送内容，从savedSolution中获取解决人姓名
		String sendInfo = buildSolutionSendInfo(savedSolution);
		solutionSend.setSendInfo(sendInfo);

		// 设置预期发送时间为当前时间（立即发送）
		solutionSend.setExpectedSendTime(new Date());

		// 设置为未发送状态
		solutionSend.setSendStatus(false);

		return save(solutionSend);
	}

	/**
	 * 构建发送信息
	 * @param solution
	 * @return
	 */
	private String buildSolutionSendInfo(FeedbackTriggerSolution solution) {
		FeedbackTriggerRecord triggerRecord = feedbackTriggerRecordService.findById(solution.getTriggerRecordId());

		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");

		return """
				解决方案通知
				线体编码: %s
				异常名称: %s
				异常描述: %s
				触发时间: %s
				解决时间: %s
				解决人: %s
				解决方案: %s
				"""
			.formatted(Optional.ofNullable(triggerRecord.getLineCode()).orElse(""),
					Optional.ofNullable(triggerRecord.getAnomaliesName()).orElse(""),
					Optional.ofNullable(triggerRecord.getAnomaliesDetail()).orElse(""),
					Optional.ofNullable(triggerRecord.getTriggerTime()).map(dateFormat::format).orElse(""),
					Optional.ofNullable(solution.getSolveTime()).map(dateFormat::format).orElse(""),
					Optional.ofNullable(solution.getSolverName()).orElse(""),
					Optional.ofNullable(solution.getSolution()).orElse(""))
			.trim();
	}

}
