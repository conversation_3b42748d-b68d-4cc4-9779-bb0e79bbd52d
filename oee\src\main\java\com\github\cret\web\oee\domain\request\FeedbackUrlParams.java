package com.github.cret.web.oee.domain.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 反馈URL构建参数封装类
 */
public class FeedbackUrlParams {

	/**
	 * 操作类型（edit-编辑/填写解决方案, view-查看解决方案, close-关闭异常）
	 */
	@NotBlank(message = "操作类型不能为空")
	private String action;

	/**
	 * 触发记录ID
	 */
	@NotBlank(message = "触发记录ID不能为空")
	private String triggerRecordId;

	/**
	 * 发送记录ID
	 */
	@NotBlank(message = "发送记录ID不能为空")
	private String sendRecordId;

	/**
	 * 用户ID
	 */
	@NotBlank(message = "用户ID不能为空")
	private String userId;

	/**
	 * 用户名称
	 */
	@NotBlank(message = "用户名称不能为空")
	private String userName;

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getTriggerRecordId() {
		return triggerRecordId;
	}

	public void setTriggerRecordId(String triggerRecordId) {
		this.triggerRecordId = triggerRecordId;
	}

	public String getSendRecordId() {
		return sendRecordId;
	}

	public void setSendRecordId(String sendRecordId) {
		this.sendRecordId = sendRecordId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

}
