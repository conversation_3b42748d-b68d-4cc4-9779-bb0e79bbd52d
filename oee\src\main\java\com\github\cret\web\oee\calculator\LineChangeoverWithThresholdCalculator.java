package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.LineChangeoverBlock;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.domain.analyze.LineChangeoverRecord;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.utils.OeeUtil;

/*
 * 换线信息计算器，换线时间超过半小时的时间认为是停机时间。
 */
public class LineChangeoverWithThresholdCalculator {

	// 半小时
	private static Integer THRESHOLD_SECONDS = 30 * 60;

	/**
	 * 获取换线记录
	 * @param primarySmtDevices
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static List<LineChangeoverRecord> getLineChangeoverInfo(Map<String, Device> primarySmtDevices,
			AnalyzeQuery query, MongoTemplate mongoTemplate) {
		if (primarySmtDevices == null || primarySmtDevices.isEmpty()) {
			throw new IllegalArgumentException("未找到设备");
		}
		Device device = primarySmtDevices.values().iterator().next();
		return switch (device.getType()) {
			case smt_npm_reporter -> getNpmSmtLineChangeoverRecord(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpmSmt2LineChangeOverRecord(primarySmtDevices, query, mongoTemplate);
			case smt_samsung -> getSamsungLineChangeoverRecord(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaLineChangeoverRecord(device, query, mongoTemplate);
			default -> throw new UnsupportedOperationException("设备类型 " + device.getType() + " 未实现换线次数查询");
		};
	}

	/**
	 * 根据换线记录计算换线信息 如果换线时间超过阈值，超过的部分算作停机时间
	 * @param records 换线记录列表
	 * @return 换线信息
	 */
	public static LineChangeoverInfo calculateLineChangeInfo(List<LineChangeoverRecord> records) {
		if (records == null || records.isEmpty()) {
			return new LineChangeoverInfo();
		}

		LineChangeoverInfo info = new LineChangeoverInfo();
		List<LineChangeoverBlock> blocks = new ArrayList<>();
		long totalChangeoverTime = 0;
		int totalStopTime = 0;

		for (int i = 0; i < records.size(); i++) {
			LineChangeoverRecord record = records.get(i);
			long interval = record.getInterval();

			// 创建换线块
			LineChangeoverBlock block = new LineChangeoverBlock();
			block.setGroupId(i);
			block.setStartTime(record.getStartTime());
			block.setEndTime(record.getEndTime());
			block.setTimeDifference(interval);
			blocks.add(block);

			// 计算换线时间和停机时间
			if (interval > THRESHOLD_SECONDS) {
				totalChangeoverTime += THRESHOLD_SECONDS;
				totalStopTime += (interval - THRESHOLD_SECONDS);
			}
			else {
				totalChangeoverTime += interval;
			}
		}

		info.setChangeoverNum(records.size());
		info.setChangeoverTime(totalChangeoverTime);
		info.setChangeoverStopTime(totalStopTime);
		info.setBlocks(blocks);

		return info;
	}

	/**
	 * 获取松下贴片机的换线记录
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<LineChangeoverRecord> getNpmSmtLineChangeoverRecord(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 添加排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加窗口操作以获取前一个产品型号和计数
		Document windowFields = new Document("$setWindowFields",
				new Document().append("partitionBy", null)
					.append("sortBy", new Document("time", 1).append("totaltime", -1))
					.append("output",
							new Document()
								.append("prevproductmodel",
										new Document("$shift",
												new Document("output", "$productmodel").append("by", -1)))
								.append("prevcount", new Document("$shift",
										new Document("output", "$raw.count.board").append("by", -1)))));
		pipeline.add(windowFields);

		// 4. 移除前一个计数为null的记录
		pipeline.add(new Document("$match", new Document("prevcount", new Document("$ne", null))));

		// 5. 添加计算字段C和isDifferentProduct
		pipeline.add(new Document("$addFields",
				new Document()
					.append("C", new Document("$cond",
							new Document("if", new Document("$gt", Arrays.asList("$raw.count.board", "$prevcount")))
								.append("then",
										new Document("$subtract", Arrays.asList("$raw.count.board", "$prevcount")))
								.append("else", 0)))
					.append("isDifferentProduct",
							new Document("$ne", Arrays.asList("$productmodel", "$prevproductmodel")))));

		// 6. 添加分组标识符
		Document groupIdWindow = new Document("$setWindowFields", new Document().append("partitionBy", null)
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document().append("groupId",
					new Document("$sum",
							new Document("$cond", Arrays.asList(new Document("$ne", Arrays.asList("$C", 0)), 1, 0)))
						.append("window", new Document("documents", Arrays.asList("unbounded", "current"))))));
		pipeline.add(groupIdWindow);

		// 7. 筛选C为0的记录
		pipeline.add(new Document("$match", new Document("C", 0)));

		// 8. 按分组标识符分组
		pipeline.add(new Document("$group",
				new Document().append("_id", "$groupId")
					.append("firstDoc", new Document("$first", "$$ROOT"))
					.append("lastDoc", new Document("$last", "$$ROOT"))
					.append("allC", new Document("$push", "$C"))
					.append("hasLineChange", new Document("$push", "$isDifferentProduct"))
					.append("count", new Document("$sum", 1))));

		// 9. 筛选有效的换线期间
		pipeline.add(new Document("$match",
				new Document("$expr",
						new Document("$and",
								Arrays.asList(new Document("$eq", Arrays.asList(new Document("$max", "$allC"), 0)),
										new Document("$anyElementTrue", "$hasLineChange"))))));

		// 10. 计算时间差和其他差值
		pipeline
			.add(new Document("$addFields", new Document()
				.append("difference",
						new Document("$subtract",
								Arrays.asList("$lastDoc.raw.count.board", "$firstDoc.raw.count.board")))
				.append("timeDifference", new Document("$subtract", Arrays.asList("$lastDoc.time", "$firstDoc.time")))
				.append("startTime", "$firstDoc.time")
				.append("endTime", "$lastDoc.time")));

		// 11. 格式化输出
		pipeline.add(new Document("$project",
				new Document().append("_id", 0)
					.append("timeDifference", 1)
					.append("startTime", 1)
					.append("endTime", 1)));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<LineChangeoverRecord> list = new ArrayList<>();

		for (Document doc : results) {
			LineChangeoverRecord record = new LineChangeoverRecord();
			record.setStartTime(doc.getDate("startTime"));
			record.setEndTime(doc.getDate("endTime"));
			// 将timeDifference转换为秒
			record.setInterval(doc.getLong("timeDifference") / 1000);
			list.add(record);
		}

		return list;
	}

	/**
	 * 获取松下贴片机的双轨的换线记录
	 * @param primarySmtDevices
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<LineChangeoverRecord> getNpmSmt2LineChangeOverRecord(Map<String, Device> primarySmtDevices,
			AnalyzeQuery query, MongoTemplate mongoTemplate) {

		List<List<LineChangeoverRecord>> allDeviceRecords = new ArrayList<>();

		// 获取每个设备的换线记录
		for (Device device : primarySmtDevices.values()) {
			List<LineChangeoverRecord> records = getNpmSmtLineChangeoverRecord(device, query, mongoTemplate);
			if (records == null || records.isEmpty()) {
				return new ArrayList<>(); // 任一设备无换线记录则直接返回空
			}
			allDeviceRecords.add(records);
		}

		List<LineChangeoverRecord> result = new ArrayList<>();
		List<LineChangeoverRecord> baseRecords = allDeviceRecords.get(0);

		// 以第一个设备的换线记录为基准
		blockLoop: for (LineChangeoverRecord base : baseRecords) {
			java.util.Date windowStart = base.getStartTime();
			java.util.Date windowEnd = base.getEndTime();

			// 与其他设备的换线块进行比对
			for (int i = 1; i < allDeviceRecords.size(); i++) {
				List<LineChangeoverRecord> compareRecords = allDeviceRecords.get(i);
				boolean hasOverlap = false;

				for (LineChangeoverRecord compare : compareRecords) {
					java.util.Date overlapStart = windowStart.after(compare.getStartTime()) ? windowStart
							: compare.getStartTime();
					java.util.Date overlapEnd = windowEnd.before(compare.getEndTime()) ? windowEnd
							: compare.getEndTime();

					if (overlapStart.before(overlapEnd)) {
						// 收缩窗口到重叠区域
						windowStart = overlapStart;
						windowEnd = overlapEnd;
						hasOverlap = true;
						break;
					}
				}
				if (!hasOverlap) {
					continue blockLoop;
				}
			}

			// 所有设备都有重叠时，生成新的换线记录
			if (windowStart.before(windowEnd)) {
				LineChangeoverRecord record = new LineChangeoverRecord();
				record.setStartTime(windowStart);
				record.setEndTime(windowEnd);
				record.setInterval((windowEnd.getTime() - windowStart.getTime()) / 1000);
				result.add(record);
			}
		}

		return result;
	}

	/**
	 * 获取三星贴片机的换线记录
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<LineChangeoverRecord> getSamsungLineChangeoverRecord(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和normalflag
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()))
			.append("normalflag", true);
		pipeline.add(new Document("$match", matchDoc));

		// 2. 排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. setWindowFields 获取前一条型号和结束时间
		Document windowFields = new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output",
						new Document()
							.append("prevModel",
									new Document("$shift",
											new Document("output", "$productmodel").append("by", -1)
												.append("default", null)))
							.append("prevTime", new Document("$shift",
									new Document("output", "$endtime").append("by", -1).append("default", null)))));
		pipeline.add(windowFields);

		// 4. addFields 判断是否换线，并计算换线信息
		Document isChangeoverCond = new Document("$cond",
				new Document("if",
						new Document("$and",
								Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevModel")),
										new Document("$ne", Arrays.asList("$prevModel", null)))))
					.append("then", 1)
					.append("else", 0));

		Document changeoverInfoCond = new Document("$cond",
				new Document("if",
						new Document("$and",
								Arrays.asList(
										new Document("$ne", Arrays.asList("$productmodel", "$prevModel")), new Document(
												"$ne", Arrays.asList("$prevModel", null)))))
					.append("then", new Document().append("startTime", "$prevTime")
						.append("endTime", "$starttime")
						.append("interval",
								new Document("$divide", Arrays
									.asList(new Document("$subtract", Arrays.asList("$starttime", "$prevTime")), 1000)))
						.append("fromModel", "$prevModel")
						.append("toModel", "$productmodel"))
					.append("else", null));

		pipeline.add(new Document("$addFields",
				new Document().append("isChangeover", isChangeoverCond).append("changeoverInfo", changeoverInfoCond)));

		// 5. 仅保留换线记录
		pipeline.add(new Document("$match", new Document("isChangeover", 1)));

		// 6. 投影换线信息
		pipeline.add(new Document("$project",
				new Document("_id", 0).append("changeoverInfo.startTime", 1)
					.append("changeoverInfo.endTime", 1)
					.append("changeoverInfo.interval", 1)
					.append("changeoverInfo.fromModel", 1)
					.append("changeoverInfo.toModel", 1)));

		// 执行聚合
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<LineChangeoverRecord> list = new ArrayList<>();
		for (Document doc : results) {
			Document info = (Document) doc.get("changeoverInfo");
			if (info == null)
				continue;
			LineChangeoverRecord record = new LineChangeoverRecord();
			record.setStartTime(info.getDate("startTime"));
			record.setEndTime(info.getDate("endTime"));
			record.setInterval((long) info.getDouble("interval").intValue());
			// 可选：如果需要 fromModel/toModel 可扩展 LineChangeoverRecord
			list.add(record);
		}
		return list;
	}

	/**
	 * 获取雅马哈贴片机的换线记录
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	private static List<LineChangeoverRecord> getYamahaLineChangeoverRecord(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		pipeline.add(new Document("$match", matchDoc));

		// 2. 排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. setWindowFields 获取前一条型号和时间
		Document windowFields = new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1)).append("output",
						new Document()
							.append("prevModel",
									new Document("$shift",
											new Document("output", "$productmodel").append("by", -1)
												.append("default", null)))
							.append("prevTime", new Document("$shift",
									new Document("output", "$time").append("by", -1).append("default", null)))));
		pipeline.add(windowFields);

		// 4. addFields 判断是否换线，并计算换线信息
		Document isChangeoverCond = new Document("$cond",
				new Document("if",
						new Document("$and",
								Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevModel")),
										new Document("$ne", Arrays.asList("$prevModel", null)))))
					.append("then", 1)
					.append("else", 0));

		Document changeoverInfoCond = new Document("$cond", new Document("if",
				new Document("$and",
						Arrays.asList(new Document("$ne", Arrays.asList("$productmodel", "$prevModel")),
								new Document("$ne", Arrays.asList("$prevModel", null)))))
			.append("then", new Document().append("startTime", "$prevTime")
				.append("endTime", "$productionstarttime")
				.append("interval",
						new Document("$divide", Arrays.asList(
								new Document("$subtract", Arrays.asList("$productionstarttime", "$prevTime")), 1000)))
				.append("fromModel", "$prevModel")
				.append("toModel", "$productmodel"))
			.append("else", null));

		pipeline.add(new Document("$addFields",
				new Document().append("isChangeover", isChangeoverCond).append("changeoverInfo", changeoverInfoCond)));

		// 5. 仅保留换线记录
		pipeline.add(new Document("$match", new Document("isChangeover", 1)));

		// 6. 投影换线信息
		pipeline.add(new Document("$project",
				new Document("_id", 0).append("changeoverInfo.startTime", 1)
					.append("changeoverInfo.endTime", 1)
					.append("changeoverInfo.interval", 1)
					.append("changeoverInfo.fromModel", 1)
					.append("changeoverInfo.toModel", 1)));

		// 执行聚合
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		List<LineChangeoverRecord> list = new ArrayList<>();
		for (Document doc : results) {
			Document info = (Document) doc.get("changeoverInfo");
			if (info == null)
				continue;
			LineChangeoverRecord record = new LineChangeoverRecord();
			record.setStartTime(info.getDate("startTime"));
			record.setEndTime(info.getDate("endTime"));
			record.setInterval((long) info.getDouble("interval").intValue());
			// 可选：如果需要 fromModel/toModel 可扩展 LineChangeoverRecord
			list.add(record);
		}
		return list;
	}

}