package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.AoiProductionGroup;
import com.github.cret.web.oee.domain.analyze.calculate.ProductionGroup;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.query.DeviceQuery;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.OeeUtil;

public class DeviceActualProduction {

	public static Map<String, Integer> getDeviceProduction(DeviceQuery deviceQuery, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		switch (deviceQuery.getType()) {
			case smt_npm_reporter:
				// 获取松下贴片机的运行时间
				List<ProductionGroup> npmSmtActualProduction = getNpmSmtActualProduction(deviceQuery.getCode(), query,
						mongoTemplate);
				return npmSmtActualProduction.stream()
					.collect(Collectors.groupingBy(ProductionGroup::getProductModel,
							Collectors.summingInt(ProductionGroup::getProductionNum)));
			default:
				break;
		}
		return null;
	}

	public static List<ProductionGroup> getNpmSmtActualProduction(String deviceCode, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(deviceCode);

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}

		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields", new Document()
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document().append("previousValue",
					new Document("$shift", new Document().append("output", "$raw.count.board").append("by", -1))))));

		// 4. 添加计算字段
		pipeline
			.add(new Document("$addFields",
					new Document("result",
							new Document("$cond",
									new Document()
										.append("if",
												new Document("$gte",
														Arrays.asList("$raw.count.board", "$previousValue")))
										.append("then",
												new Document("$subtract",
														Arrays.asList("$raw.count.board", "$previousValue")))
										.append("else", "$raw.count.board")))));

		// 5. 过滤掉第一条记录
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 汇总计算结果
		pipeline.add(new Document("$group",
				new Document().append("_id", "$productmodel").append("total", new Document("$sum", "$result"))));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为ActualRunTime对象列表
		List<ProductionGroup> list = new ArrayList<>();

		for (Document doc : results) {
			ProductionGroup productionGroup = BuilderUtil.builder(ProductionGroup::new)
				.with(ProductionGroup::setDeviceCode, deviceCode)
				.with(ProductionGroup::setProductModel, doc.getString("_id"))
				.with(ProductionGroup::setProductionNum, doc.getInteger("total"))
				.build();
			list.add(productionGroup);
		}

		return list;
	}

	/**
	 * 计算松下贴片机的实际生产数量
	 * @param device
	 * @param query
	 * @return
	 */
	public static List<AoiProductionGroup> getNpmSmtActualProduction(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}

		pipeline.add(new Document("$match", matchDoc));
		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields", new Document()
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document().append("previousValue",
					new Document("$shift", new Document().append("output", "$raw.count.board").append("by", -1))))));

		// 4. 添加计算字段
		pipeline
			.add(new Document("$addFields",
					new Document("result",
							new Document("$cond",
									new Document()
										.append("if",
												new Document("$gte",
														Arrays.asList("$raw.count.board", "$previousValue")))
										.append("then",
												new Document("$subtract",
														Arrays.asList("$raw.count.board", "$previousValue")))
										.append("else", "$raw.count.board")))));

		// 5. 过滤掉第一条记录
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 汇总计算结果
		pipeline.add(new Document("$group",
				new Document().append("_id", "$productmodel").append("total", new Document("$sum", "$result"))));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 转换结果为ActualRunTime对象列表
		List<AoiProductionGroup> actualProductionGroups = new ArrayList<>();

		for (Document doc : results) {
			AoiProductionGroup actualProductionGroup = new AoiProductionGroup();
			actualProductionGroup.setProductModel(doc.getString("_id"));
			actualProductionGroup.setActualProduction(doc.getInteger("total"));
			actualProductionGroups.add(actualProductionGroup);
		}

		return actualProductionGroups;
	}

	/**
	 * 计算松下贴片机的实际生产数量
	 * @param device 设备对象
	 * @param query 查询参数
	 * @param mongoTemplate MongoDB 操作模板
	 * @return 实际生产数量
	 */
	public static Integer getNpmSmtActualProductionWidthModule(Device device, AnalyzeQuery query,
			MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}

		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields", new Document()
			.append("sortBy", new Document("time", 1).append("totaltime", -1))
			.append("output", new Document("previousValue",
					new Document("$shift", new Document().append("output", "$raw.count.module").append("by", -1))))));

		// 4. 添加计算字段
		pipeline
			.add(new Document("$addFields",
					new Document("result",
							new Document("$cond",
									new Document()
										.append("if",
												new Document("$gte",
														Arrays.asList("$raw.count.module", "$previousValue")))
										.append("then",
												new Document("$subtract",
														Arrays.asList("$raw.count.module", "$previousValue")))
										.append("else", "$raw.count.module")))));

		// 5. 过滤掉第一条记录
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 汇总计算结果
		pipeline.add(new Document("$group",
				new Document().append("_id", null).append("total", new Document("$sum", "$result"))));

		// 执行聚合查询
		List<Document> results = mongoTemplate.getCollection(collectionName)
			.aggregate(pipeline)
			.into(new ArrayList<>());

		// 获取总生产数量
		if (!results.isEmpty()) {
			Document doc = results.get(0);
			return doc.getInteger("total", 0); // 使用默认值防止 null 异常
		}

		return 0; // 如果没有结果，返回 0
	}

}
