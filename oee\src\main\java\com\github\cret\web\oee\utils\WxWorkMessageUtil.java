package com.github.cret.web.oee.utils;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.github.cret.web.oee.config.WxWorkConfig;
import com.github.cret.web.oee.domain.wxwork.MsgRes;
import com.github.cret.web.oee.domain.wxwork.R;

/**
 * 企业微信消息发送工具类
 */
public class WxWorkMessageUtil {

	private static final Logger logger = LoggerFactory.getLogger(WxWorkMessageUtil.class);

	/**
	 * 发送企业微信消息
	 * @param msg 消息内容
	 * @param wxWorkConfig 企业微信配置
	 * @param restTemplate REST模板
	 * @return 发送结果
	 */
	public static R<MsgRes> sendWxWorkMsg(String msg, WxWorkConfig wxWorkConfig, RestTemplate restTemplate) {
		// 1. 参数校验
		if (!StringUtils.hasText(msg)) {
			logger.warn("消息内容不能为空");
			return R.fail("消息内容不能为空");
		}

		if (wxWorkConfig == null) {
			logger.error("企业微信配置不能为空");
			return R.fail("企业微信配置不能为空");
		}

		if (restTemplate == null) {
			logger.error("RestTemplate不能为空");
			return R.fail("RestTemplate不能为空");
		}

		// 2. 安全构建URL
		String apiUrl = UriComponentsBuilder.fromUriString(wxWorkConfig.getSendSeverUrl())
			.path(wxWorkConfig.getSendMsgApi())
			.build()
			.toUriString();

		logger.info("准备发送企业微信消息到：{} 消息内容：{}", apiUrl, msg);

		try {
			// 3. 构建请求头和请求体
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<String> request = new HttpEntity<>(msg, headers);

			// 4. 发送请求
			ResponseEntity<R<MsgRes>> response = restTemplate.exchange(apiUrl, HttpMethod.POST, request,
					new ParameterizedTypeReference<R<MsgRes>>() {
					});

			// 5. 检查HTTP状态码
			if (response.getStatusCode().is2xxSuccessful()) {
				R<MsgRes> result = response.getBody();

				// 6. 验证响应内容
				if (result != null && validateWxWorkResponse(result)) {
					logger.info("企业微信消息发送成功，响应：{}", result);
					return result;
				}
				else {
					logger.warn("企业微信接口返回了无效的响应：{}", result);
					return result != null ? result : R.fail("接口返回空响应");
				}
			}
			else {
				logger.error("消息发送失败，HTTP状态码：{} 响应体：{}", response.getStatusCode(), response.getBody());
				return R.fail("企业微信接口返回异常状态：" + response.getStatusCode());
			}
		}
		catch (RestClientException e) {
			// 7. 异常处理
			logger.error("发送企业微信消息网络异常: {}", e.getMessage(), e);
			return R.fail("网络连接异常：" + e.getMessage());
		}
		catch (Exception e) {
			// 8. 其他异常处理
			logger.error("发送企业微信消息未知异常: {}", e.getMessage(), e);
			return R.fail("系统异常：" + e.getMessage());
		}
	}

	/**
	 * 验证企业微信响应内容
	 * @param response 企业微信响应
	 * @return true表示响应有效，false表示响应无效
	 */
	private static boolean validateWxWorkResponse(R<MsgRes> response) {
		if (response == null) {
			return false;
		}

		// 检查响应是否成功
		if (response.isSuccess()) {
			return true;
		}

		// 如果响应失败，检查是否有具体的错误信息
		MsgRes msgRes = response.getData();
		if (msgRes != null) {
			// 企业微信的错误码，0表示成功
			Integer errCode = msgRes.getErrCode();
			if (errCode != null && errCode == 0) {
				return true;
			}

			// 记录具体的企业微信错误信息
			logger.warn("企业微信返回错误，错误码：{}，错误信息：{}", errCode, msgRes.getErrMsg());
		}

		return false;
	}

	/**
	 * 构建发送结果文本
	 * @param sendResult 发送结果
	 * @param targetCount 目标接收人数
	 * @return 发送结果文本
	 */
	public static String buildResultText(R<MsgRes> sendResult, int targetCount) {
		if (sendResult == null) {
			return String.format("发送失败：未收到响应（目标%d人）", targetCount);
		}

		MsgRes msgRes = sendResult.getData();
		if (msgRes == null) {
			return String.format("发送失败：响应数据为空（目标%d人）", targetCount);
		}

		// 检查企业微信错误码，0表示成功
		Integer errCode = msgRes.getErrCode();
		if (errCode == null || errCode != 0) {
			String errMsg = msgRes.getErrMsg() != null ? msgRes.getErrMsg() : "未知错误";
			return String.format("发送失败：%s（错误码:%s，目标%d人）", errMsg, errCode, targetCount);
		}

		// 分析无效用户
		List<String> invalidUsers = msgRes.getInvalidUserList();
		int invalidCount = invalidUsers != null ? invalidUsers.size() : 0;
		int successfulCount = targetCount - invalidCount;

		if (successfulCount > 0) {
			String result = String.format("发送成功，实际通知%d人", successfulCount);
			if (invalidCount > 0) {
				result += String.format("（目标%d人，无效%d人）", targetCount, invalidCount);
			}
			if (StringUtils.hasText(msgRes.getMsgId())) {
				result += String.format("，消息ID:%s", msgRes.getMsgId());
			}
			return result;
		}
		else {
			return String.format("发送失败，无有效接收人（目标%d人）", targetCount);
		}
	}

}
