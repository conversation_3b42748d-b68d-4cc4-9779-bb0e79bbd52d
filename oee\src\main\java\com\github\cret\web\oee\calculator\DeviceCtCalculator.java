package com.github.cret.web.oee.calculator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.StringUtils;

import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.utils.DocumentUtil;
import com.github.cret.web.oee.utils.OeeUtil;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;

/**
 * 设备平均CT计算器
 */
public class DeviceCtCalculator {

	/**
	 * 获取设备实际CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getDeviceCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		return switch (device.getType()) {
			case print_gkg -> getGkgPrintCt(device, query, mongoTemplate);
			case smt_npm_reporter -> getNpmSmtCt(device, query, mongoTemplate);
			case smt_npm_reporter2 -> getNpmSmtCt(device, query, mongoTemplate);
			case smt_samsung -> getSamsungSmtCt(device, query, mongoTemplate);
			case smt_yamaha -> getYamahaSmtCt(device, query, mongoTemplate);
			case aoi_yamaha -> getYamahaAoiCt(device, query, mongoTemplate);
			case aoi_viscom -> getViscomAoiCt(device, query, mongoTemplate);
			case aoi_jutze -> getJutzeAoiCt(device, query, mongoTemplate);
			case aoi_delu -> getDeluAoiCt(device, query, mongoTemplate);
			default -> 0.00;
		};
	}

	/**
	 * 获取GKG印刷机的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	public static Double getGkgPrintCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取设备CT日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time").gte(query.getStartTime()).lte(query.getEndTime());

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 使用聚合管道计算平均CT
		Aggregation aggregation = Aggregation.newAggregation(
				// 1. 匹配时间范围和产品型号（如果有）
				Aggregation.match(matchCriteria),
				// 2. 计算字段a和字段b的和的平均值
				Aggregation.group().avg(ArithmeticOperators.Add.valueOf("ct").add("cleantime")).as("averageCt"),
				// 3. 格式化结果，保留两位小数
				Aggregation.project()
					.andExclude("_id")
					.and(ArithmeticOperators.Round.roundValueOf("averageCt").place(2))
					.as("averageCt"));

		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		if (!results.iterator().hasNext()) {
			return 0.00;
		}

		Document result = results.iterator().next();
		Double averageCt = result.getDouble("averageCt");
		return averageCt != null ? averageCt : 0.00;
	}

	/**
	 * 获取松下贴片机的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	public static Double getNpmSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);

		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取松下贴片机的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	@Deprecated
	public static Double getNpmSmtCt1(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}
		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 设置窗口获取前值
		Document windowOutput = new Document()
			.append("previousStopTime", new Document("$shift", new Document("output", "$stoptime").append("by", -1)))
			.append("previousRunTime", new Document("$shift", new Document("output", "$actualtime").append("by", -1)))
			.append("previousTotalTime", new Document("$shift", new Document("output", "$totaltime").append("by", -1)))
			.append("previousBoardCount",
					new Document("$shift", new Document("output", "$raw.count.board").append("by", -1)));

		pipeline.add(new Document("$setWindowFields",
				new Document("sortBy", new Document("time", 1).append("actualtime", -1)).append("output",
						windowOutput)));

		// 4. 添加计算字段
		pipeline
			.add(new Document("$addFields",
					new Document()
						.append("stoptimeResult",
								new Document("$cond",
										new Document()
											.append("if",
													new Document("$gte",
															Arrays.asList("$stoptime", "$previousStopTime")))
											.append("then",
													new Document("$subtract",
															Arrays.asList("$stoptime", "$previousStopTime")))
											.append("else", "$stoptime")))
						.append("runtimeResult",
								new Document("$cond",
										new Document()
											.append("if",
													new Document("$gte",
															Arrays.asList("$actualtime", "$previousRunTime")))
											.append("then",
													new Document("$subtract",
															Arrays.asList("$actualtime", "$previousRunTime")))
											.append("else", "$actualtime")))
						.append("boardResult",
								new Document("$cond",
										new Document()
											.append("if",
													new Document("$gte",
															Arrays.asList("$raw.count.board", "$previousBoardCount")))
											.append("then",
													new Document("$subtract",
															Arrays.asList("$raw.count.board", "$previousBoardCount")))
											.append("else", "$raw.count.board")))));

		// 5. 过滤掉首条记录
		pipeline.add(new Document("$match",
				new Document("$and", List.of(new Document("boardResult", new Document("$ne", null))))));

		// 6. 分组计算总和
		pipeline.add(new Document("$group",
				new Document().append("_id", null)
					.append("stopTime", new Document("$sum", "$stoptimeResult"))
					.append("runTime", new Document("$sum", "$runtimeResult"))
					.append("totalBoards", new Document("$sum", "$boardResult"))));

		// 7. 计算CT
		pipeline.add(new Document("$project",
				new Document().append("actualCT", new Document("$cond",
						new Document().append("if", new Document("$eq", Arrays.asList("$totalBoards", 0)))
							.append("then", 0.0)
							.append("else", new Document("$divide",
									Arrays.asList(new Document("$add", List.of("$runTime")), "$totalBoards")))))));

		// 执行聚合查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		if (result == null) {
			return 0.0;
		}

		// 获取计算结果并保留2位小数
		Double actualCT = result.getDouble("actualCT");
		return actualCT != null ? Math.round(actualCT * 100.0) / 100.0 : 0.0;
	}

	/**
	 * 获取三星贴片的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	public static Double getSamsungSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);

		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取三星贴片机的设备CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	@Deprecated
	public static Double getSamsungSmtCt1(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取设备对应的生产日志集合名称
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建MongoDB聚合管道
		List<Bson> pipeline = new ArrayList<>();

		/*----- 第一阶段：构建基础过滤条件 -----*/
		List<Bson> matchConditions = new ArrayList<>();
		// 时间范围过滤：大于等于开始时间，小于等于结束时间
		matchConditions.add(Filters.gte("time", query.getStartTime()));
		matchConditions.add(Filters.lte("time", query.getEndTime()));
		// 状态过滤：仅选择正常状态记录（注意：lte用于布尔值可能有特殊业务含义）
		matchConditions.add(Filters.lte("normalflag", true));

		// 产品型号过滤：如果查询条件包含产品型号
		if (StringUtils.hasText(query.getProductModel())) {
			matchConditions.add(Filters.eq("productmodel", query.getProductModel()));
		}

		// 添加组合过滤条件到管道
		pipeline.add(Aggregates.match(Filters.and(matchConditions)));

		/*----- 第二阶段：数据完整性过滤 -----*/
		// 确保关键字段存在且不为空值
		pipeline.add(Aggregates
			.match(Filters.and(Filters.exists("samsungindex.runtime"), Filters.ne("samsungindex.runtime", null),
					Filters.exists("samsungindex.panelcount"), Filters.ne("samsungindex.panelcount", null))));

		/*----- 第三阶段：数据汇总计算 -----*/
		// 按空分组（即全量汇总），计算总运行时间和总面板数量
		pipeline.add(Aggregates.group(null, // 空分组表示聚合所有文档
				Accumulators.sum("totalRuntime", "$samsungindex.runtime"),
				Accumulators.sum("totalPanelCount", "$samsungindex.panelcount")));

		/*----- 第四阶段：CT值计算 -----*/
		// 计算实际CT值（总运行时间/总面板数），并处理除零情况
		pipeline.add(Aggregates.project(Projections.fields(Projections.computed("ct",
				new Document("$cond",
						new Document().append("if", new Document("$ne", Arrays.asList("$totalPanelCount", 0))) // 判断分母非零
							.append("then", new Document("$divide", Arrays.asList("$totalRuntime", "$totalPanelCount"))) // 计算CT
							.append("else", 0) // 异常情况返回0
				)), Projections.excludeId() // 排除_id字段
		)));

		/*----- 执行聚合查询并处理结果 -----*/
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		// 处理空结果情况
		if (null == result) {
			return 0.0;
		}

		// 获取计算结果并保留两位小数
		Double actualCT = result.getDouble("ct");
		return actualCT != null ? Math.round(actualCT * 100.0) / 100.0 : 0.0; // 四舍五入到小数点后两位
	}

	/**
	 * 获取雅马哈贴片机的设备CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	public static Double getYamahaSmtCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);

		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取雅马哈贴片机的设备CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	@Deprecated
	public static Double getYamahaSmtCt1(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {

		List<Document> pipeline = new ArrayList<>();

		// 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));
		pipeline.add(new Document("$match", matchDoc));

		// 如果指定了产品型号，添加产品型号过滤条件（过滤productmodel去掉最后一个“-”及其后面的数据后的值）
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			pipeline.add(new Document("$match", new Document("productmodel", query.getProductModel())));
		}

		// 按时间倒序排序
		pipeline.add(new Document("$sort", new Document("time", -1)));

		// 预处理文档并收集到数组
		Document groupStage1 = new Document("$group",
				new Document("_id", null).append("docs",
						new Document("$push",
								new Document("productmodel", "$productmodel").append("batch", "$batchsequencenumber")
									.append("ct", new Document("$add",
											Arrays.asList("$mountingcta", "$mountingctb", "$mountingctc",
													"$mountingctd", "$markrecognitioncta", "$markrecognitionctb",
													"$markrecognitionctc", "$markrecognitionctd", "$transferct"))))));
		pipeline.add(groupStage1);

		// 查找首个型号变更点
		Document reduceStage = new Document("$reduce",
				new Document("input", new Document("$range", Arrays.asList(1, new Document("$size", "$docs"))))
					.append("initialValue", -1)
					.append("in",
							new Document("$cond", Arrays.asList(
									new Document("$and",
											Arrays.asList(new Document("$eq", Arrays.asList("$$value", -1)),
													new Document("$ne", Arrays.asList(
															new Document("$arrayElemAt",
																	Arrays.asList("$docs.productmodel", "$$this")),
															new Document("$arrayElemAt",
																	Arrays.asList("$docs.productmodel",
																			new Document("$subtract",
																					Arrays.asList("$$this", 1)))))))),
									"$$this", "$$value"))));
		pipeline.add(new Document("$addFields", new Document("firstChangeIndex", reduceStage)));

		// 数据截断处理
		Document sliceOperation = new Document("$cond",
				Arrays.asList(new Document("$ne", Arrays.asList("$firstChangeIndex", -1)),
						new Document("$slice",
								Arrays.asList("$docs", 0, new Document("$add", Arrays.asList("$firstChangeIndex", 1)))),
						"$docs"));
		pipeline.add(new Document("$project", new Document("filteredDocs", sliceOperation)));

		// 展开文档
		pipeline.add(new Document("$unwind", "$filteredDocs"));
		pipeline.add(new Document("$replaceRoot", new Document("newRoot", "$filteredDocs")));

		// 分组计算最大值
		Document groupStage2 = new Document("$group",
				new Document("_id", "$batch").append("maxCt", new Document("$max", "$ct")));
		pipeline.add(groupStage2);

		// 最终聚合
		Document finalGroup = new Document("$group", new Document("_id", null).append("number", new Document("$sum", 1))
			.append("totalTime", new Document("$sum", "$maxCt")));
		pipeline.add(finalGroup);

		// 结果格式化
		pipeline.add(new Document("$project", new Document("_id", 0).append("number", 1).append("totalTime", 1)));

		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 执行聚合查询并输出结果
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();
		if (null == result) {
			return 0.0;
		}

		double totalTime = DocumentUtil.getDouble(result.get("totalTime"));
		double number = DocumentUtil.getDouble(result.get("number"));
		return number != 0.0 ? Math.round((totalTime / number) * 100.0) / 100.0 : 0.0;
	}

	/**
	 * 获取雅马哈AOI的CT
	 * @param device
	 * @param query
	 * @param mongoTemplate
	 * @return
	 */
	public static Double getYamahaAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取贴片机生产数据
		List<ProductionData> deviceProductionData = BaseDataCalculator.getDeviceProductionData(device, query,
				mongoTemplate);

		// 获取分组ID最大的产品CT
		Double result = deviceProductionData.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取雅马哈AOI的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	// TODO 使用日志查询的AOI数据不准确
	@Deprecated
	public static Double getYamahaAoiCt1(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建聚合管道
		List<Document> pipeline = new ArrayList<>();

		// 1. 匹配时间范围和产品型号（如果有）
		Document matchDoc = new Document("time",
				new Document("$gte", query.getStartTime()).append("$lte", query.getEndTime()));

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchDoc.append("productmodel", query.getProductModel());
		}

		pipeline.add(new Document("$match", matchDoc));

		// 2. 按时间排序
		pipeline.add(new Document("$sort", new Document("time", 1)));

		// 3. 添加前一个值字段
		pipeline.add(new Document("$setWindowFields",
				new Document().append("sortBy", new Document("time", 1))
					.append("output", new Document().append("previousValue",
							new Document("$shift", new Document().append("output", "$time").append("by", -1))))));

		// 4. 添加计算字段 - 转换为毫秒并计算时间差
		pipeline
			.add(new Document("$addFields",
					new Document("result",
							new Document("$cond",
									new Document()
										.append("if",
												new Document("$gte",
														Arrays.asList(new Document("$toLong", "$time"),
																new Document("$toLong", "$previousValue"))))
										.append("then",
												new Document("$divide", Arrays.asList(
														new Document("$subtract",
																Arrays.asList(new Document("$toLong", "$time"),
																		new Document("$toLong", "$previousValue"))),
														1000 // 转换为秒
												)))
										.append("else", 0)))));

		// 5. 过滤掉第一条记录
		pipeline.add(new Document("$match", new Document("result", new Document("$ne", null))));

		// 6. 计算平均值而不是总和
		pipeline.add(new Document("$group",
				new Document().append("_id", null).append("avgCt", new Document("$avg", "$result"))));

		// 7. 格式化结果，保留2位小数
		pipeline.add(new Document("$project",
				new Document().append("_id", 0).append("avgCt", new Document("$round", Arrays.asList("$avgCt", 2)))));

		// 执行聚合查询
		Document result = mongoTemplate.getCollection(collectionName).aggregate(pipeline).first();

		if (result == null) {
			return 0.0;
		}

		Double avgCt = result.getDouble("avgCt");
		return avgCt != null ? avgCt : 0.0;
	}

	/**
	 * 获取维视AOI的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	// TODO 使用日志查询的AOI数据不准确
	public static Double getViscomAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产数据
		List<ProductionData> datas = BaseDataCalculator.getDeviceProductionData(device, query, mongoTemplate);

		// datas 中groupId最大的对象数据。
		Double result = datas.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取维视AOI的CT
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	// TODO 使用日志查询的AOI数据不准确
	@Deprecated
	public static Double getViscomAoiCt1(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取设备CT日志集合
		String collectionName = OeeUtil.getProductionLogCollection(device.getCode());

		// 构建基础匹配条件
		Criteria matchCriteria = Criteria.where("time").gte(query.getStartTime()).lte(query.getEndTime());

		// 如果指定了产品型号，添加产品型号过滤条件
		if (query.getProductModel() != null && !query.getProductModel().isEmpty()) {
			matchCriteria.and("productmodel").is(query.getProductModel());
		}

		// 使用聚合管道计算平均CT
		Aggregation aggregation = Aggregation.newAggregation(
				// 1. 匹配时间范围和产品型号（如果有）
				Aggregation.match(matchCriteria),
				// 2. 计算每条记录的时间差（毫秒转秒）并求均
				Aggregation.group()
					.avg(ArithmeticOperators.Divide
						.valueOf(ArithmeticOperators.Subtract.valueOf("$raw.endtime").subtract("$raw.starttime"))
						.divideBy(1000))
					.as("averageCt"),
				// 3. 格式化结果，保留两位小数
				Aggregation.project()
					.andExclude("_id")
					.and(ArithmeticOperators.Round.roundValueOf("averageCt").place(2))
					.as("averageCt"));

		AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, collectionName, Document.class);

		if (!results.iterator().hasNext()) {
			return 0.00;
		}

		Document result = results.iterator().next();
		Double averageCt = result.getDouble("averageCt");
		return averageCt != null ? averageCt : 0.00;
	}

	/**
	 * 获取矩子AOI的平均CT（当前最新的产品）
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	// TODO 使用日志查询的AOI数据不准确
	public static Double getJutzeAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产数据
		List<ProductionData> datas = BaseDataCalculator.getDeviceProductionData(device, query, mongoTemplate);

		// datas 中groupId最大的对象数据。
		Double result = datas.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

	/**
	 * 获取德律AOI的平均CT(当前最新的产品)
	 * @param device 设备信息
	 * @param query 查询条件
	 * @param mongoTemplate MongoDB模板
	 * @return 平均CT值（秒/件）
	 */
	// TODO 使用日志查询的AOI数据不准确
	public static Double getDeluAoiCt(Device device, AnalyzeQuery query, MongoTemplate mongoTemplate) {
		// 获取生产数据
		List<ProductionData> datas = BaseDataCalculator.getDeviceProductionData(device, query, mongoTemplate);

		// datas 中groupId最大的对象数据。
		Double result = datas.stream()
			.max(Comparator.comparing(ProductionData::getGroupId))
			.map(ProductionData::getActualCt)
			.orElse(0.0);

		return result;
	}

}
