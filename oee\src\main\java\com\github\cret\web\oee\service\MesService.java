package com.github.cret.web.oee.service;

import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.oee.domain.mes.StandardCapacity;

@Service
public class MesService {

	private final RestTemplate restTemplate;

	public MesService(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	public StandardCapacity fetchStandardCapacity(String baseUrl, String prodId, String plId, String bomSide) {
		// 构建请求的 URL
		String url = baseUrl + "/oee/standardCapacity?prodId=" + prodId + "&plId=" + plId + "&bomSide=" + bomSide;
		// 发送 GET 请求并接收响应
		return restTemplate.getForObject(url, StandardCapacity.class);
	}

}
