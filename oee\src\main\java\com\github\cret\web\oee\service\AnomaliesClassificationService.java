package com.github.cret.web.oee.service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.domain.anomalies.AnomaliesClassificationSearch;

public interface AnomaliesClassificationService {

	void save(AnomaliesClassification anomaliesClassification);

	void deleteById(String id);

	AnomaliesClassification findById(String id);

	PageList<AnomaliesClassification> page(PageableParam<AnomaliesClassificationSearch> param);

	/**
	 * 更新启用状态
	 */
	void updateEnable(String id, Boolean enable);

	/**
	 * 根据线体编码和异常编码查找唯一的异常配置
	 * @param lineCode 线体编码
	 * @param anomaliesCode 异常编码
	 * @return 异常配置
	 */
	AnomaliesClassification findByLineCodeAndAnomaliesCode(String lineCode, String anomaliesCode);

}
