package com.github.cret.web.oee.utils;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * URL查询参数加密工具类 使用AES-GCM算法对URL查询参数进行加密和解密
 */
public class UrlEncryptionUtil {

	private static final Logger logger = LoggerFactory.getLogger(UrlEncryptionUtil.class);

	private static final String ALGORITHM = "AES";

	private static final String TRANSFORMATION = "AES/GCM/NoPadding";

	private static final int GCM_IV_LENGTH = 12;

	private static final int GCM_TAG_LENGTH = 16;

	private static final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * 生成AES密钥
	 * @return Base64编码的密钥字符串
	 */
	public static String generateKey() {
		try {
			KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
			keyGenerator.init(256);
			SecretKey secretKey = keyGenerator.generateKey();
			return Base64.getEncoder().encodeToString(secretKey.getEncoded());
		}
		catch (Exception e) {
			logger.error("生成AES密钥失败", e);
			throw new RuntimeException("生成AES密钥失败", e);
		}
	}

	/**
	 * 加密查询参数
	 * @param params 查询参数Map
	 * @param secretKey Base64编码的密钥
	 * @return 加密后的URL安全Base64字符串
	 */
	public static String encryptParams(Map<String, String> params, String secretKey) {
		if (params == null || params.isEmpty()) {
			return "";
		}

		try {
			// 将参数Map转换为JSON字符串
			String jsonParams = objectMapper.writeValueAsString(params);

			// 解码密钥
			byte[] keyBytes = Base64.getDecoder().decode(secretKey);
			SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);

			// 生成随机IV
			byte[] iv = new byte[GCM_IV_LENGTH];
			new SecureRandom().nextBytes(iv);

			// 初始化加密器
			Cipher cipher = Cipher.getInstance(TRANSFORMATION);
			GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
			cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);

			// 加密数据
			byte[] encryptedData = cipher.doFinal(jsonParams.getBytes(StandardCharsets.UTF_8));

			// 将IV和加密数据合并
			byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
			System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
			System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);

			// 返回URL安全的Base64编码
			return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedWithIv);

		}
		catch (Exception e) {
			logger.error("加密查询参数失败: {}", params, e);
			throw new RuntimeException("加密查询参数失败", e);
		}
	}

	/**
	 * 解密查询参数
	 * @param encryptedParams 加密的参数字符串
	 * @param secretKey Base64编码的密钥
	 * @return 解密后的参数Map
	 */
	public static Map<String, String> decryptParams(String encryptedParams, String secretKey) {
		if (!StringUtils.hasText(encryptedParams)) {
			return new HashMap<>();
		}

		try {
			// 解码Base64
			byte[] encryptedWithIv = Base64.getUrlDecoder().decode(encryptedParams);

			// 分离IV和加密数据
			byte[] iv = new byte[GCM_IV_LENGTH];
			byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
			System.arraycopy(encryptedWithIv, 0, iv, 0, iv.length);
			System.arraycopy(encryptedWithIv, iv.length, encryptedData, 0, encryptedData.length);

			// 解码密钥
			byte[] keyBytes = Base64.getDecoder().decode(secretKey);
			SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);

			// 初始化解密器
			Cipher cipher = Cipher.getInstance(TRANSFORMATION);
			GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
			cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);

			// 解密数据
			byte[] decryptedData = cipher.doFinal(encryptedData);
			String jsonParams = new String(decryptedData, StandardCharsets.UTF_8);

			// 将JSON字符串转换为Map
			return objectMapper.readValue(jsonParams, new TypeReference<Map<String, String>>() {
			});

		}
		catch (Exception e) {
			logger.error("解密查询参数失败: {}", encryptedParams, e);
			throw new RuntimeException("解密查询参数失败", e);
		}
	}

	/**
	 * 构建加密的查询字符串
	 * @param params 查询参数Map
	 * @param secretKey Base64编码的密钥
	 * @return 加密的查询字符串，格式为 ?data=encryptedString
	 */
	public static String buildEncryptedQueryString(Map<String, String> params, String secretKey) {
		if (params == null || params.isEmpty()) {
			return "";
		}

		String encryptedParams = encryptParams(params, secretKey);
		return "?data=" + encryptedParams;
	}

	/**
	 * 验证密钥格式是否正确
	 * @param secretKey Base64编码的密钥
	 * @return true表示密钥格式正确，false表示格式错误
	 */
	public static boolean isValidKey(String secretKey) {
		if (!StringUtils.hasText(secretKey)) {
			return false;
		}

		try {
			byte[] keyBytes = Base64.getDecoder().decode(secretKey);
			return keyBytes.length == 32; // 256位密钥
		}
		catch (Exception e) {
			return false;
		}
	}

}
