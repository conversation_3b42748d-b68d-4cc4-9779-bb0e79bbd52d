package com.github.cret.web.oee.domain.analyze.result;

import java.util.List;

import com.github.cret.web.oee.domain.analyze.OeeResult;

/**
 * 分析结果
 */
public class AnalyzeMixResult {

	private OeeResult oeeResult;

	private List<AlarmInfo> alarmResult;

	/**
	 * 不良类型结果
	 */
	private DefectTypeResult defectTypeResult;

	public OeeResult getOeeResult() {
		return oeeResult;
	}

	public void setOeeResult(OeeResult oeeResult) {
		this.oeeResult = oeeResult;
	}

	public List<AlarmInfo> getAlarmResult() {
		return alarmResult;
	}

	public void setAlarmResult(List<AlarmInfo> alarmResult) {
		this.alarmResult = alarmResult;
	}

	public DefectTypeResult getDefectTypeResult() {
		return defectTypeResult;
	}

	public void setDefectTypeResult(DefectTypeResult defectTypeResult) {
		this.defectTypeResult = defectTypeResult;
	}

}
