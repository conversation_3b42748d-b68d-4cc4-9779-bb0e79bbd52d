package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.calculator.AoiDefectCountCalculator;
import com.github.cret.web.oee.calculator.AoiDefectTypesCalculator;
import com.github.cret.web.oee.calculator.AoiQualityCalculator;
import com.github.cret.web.oee.calculator.BaseDataCalculator;
import com.github.cret.web.oee.calculator.LineChangeoverWithThresholdCalculator;
import com.github.cret.web.oee.calculator.ProductModelCalculator;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.domain.analyze.AoiProductionGroup;
import com.github.cret.web.oee.domain.analyze.DeviceCtInfo;
import com.github.cret.web.oee.domain.analyze.DeviceDefectType;
import com.github.cret.web.oee.domain.analyze.DeviceProductModel;
import com.github.cret.web.oee.domain.analyze.LineChangeoverInfo;
import com.github.cret.web.oee.domain.analyze.LineChangeoverRecord;
import com.github.cret.web.oee.domain.analyze.ProductionData;
import com.github.cret.web.oee.domain.analyze.ProductionLineInfo;
import com.github.cret.web.oee.domain.analyze.calculate.DefectTypeInfo;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.enums.ProductionSample;
import com.github.cret.web.oee.service.BaseAnalyzeService;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.TheoreticalOutputService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.OeeUtil;

/**
 * 基础分析服务
 */
@Service
public class BaseAnalyzeServiceImpl implements BaseAnalyzeService {

	private final DeviceService deviceService;

	private final TheoreticalOutputService theoreticalOutputService;

	private MongoTemplate mongoTemplate;

	public BaseAnalyzeServiceImpl(DeviceService deviceService, MongoTemplate mongoTemplate,
			TheoreticalOutputService theoreticalOutputService) {
		this.deviceService = deviceService;
		this.mongoTemplate = mongoTemplate;
		this.theoreticalOutputService = theoreticalOutputService;
	}

	@Override
	public List<ProductionData> getDeviceProductionData(AnalyzeQuery query) {
		// 获取全部设备
		List<Device> allDevices = deviceService.getAllDevices(query.getCode());

		// 使用CompletableFuture并行处理每个设备的数据查询
		List<CompletableFuture<List<ProductionData>>> futures = allDevices.stream()
			.map(device -> CompletableFuture
				.supplyAsync(() -> BaseDataCalculator.getDeviceProductionData(device, query, mongoTemplate)))
			.collect(Collectors.toList());

		// 等待所有异步任务完成并合并结果
		return futures.stream()
			.map(CompletableFuture::join)
			.filter(Objects::nonNull)
			.flatMap(List::stream)
			.collect(Collectors.toList());
	}

	@Override
	public List<ProductionLineInfo> getCurrentProduction(AnalyzeQuery query, Map<String, Device> primarySmtDevices) {
		List<ProductionLineInfo> result = primarySmtDevices.values().parallelStream().map(device -> {
			// 获取设备生产数据并确定最新产品型号
			String currentModel = BaseDataCalculator.getDeviceProductionData(device, query, mongoTemplate)
				.stream()
				.max(Comparator.comparing(ProductionData::getGroupId))
				.map(ProductionData::getOriginalProductModel)
				.orElse("暂无产品");

			// 查询理论产量
			TheoreticalOutput theoreticalOutput = theoreticalOutputService
				.findByDeviceCodeAndProductModel(device.getCode(), currentModel);

			// 构建生产线信息
			return BuilderUtil.builder(ProductionLineInfo::new)
				.with(ProductionLineInfo::setProductModel, currentModel)
				.with(ProductionLineInfo::setTrackName, device.getTrackEnum().getName())
				.with(ProductionLineInfo::setSample,
						theoreticalOutput != null ? ProductionSample.notsample : ProductionSample.sample)
				.build();
		}).collect(Collectors.toList());
		return result;
	}

	@Override
	public Map<String, List<ProductionData>> getProductData(AnalyzeQuery query, List<Device> devices) {
		// 并行处理设备数据并收集到Map
		Map<String, List<ProductionData>> productModelDataMap = devices.parallelStream()
			.map(device -> BaseDataCalculator.getDeviceProductionDataGroup(device, query, mongoTemplate))
			.flatMap(List::stream)
			.map(data -> {
				// 查询理论产量
				TheoreticalOutput theoreticalOutput = theoreticalOutputService
					.findByDeviceCodeAndProductModel(data.getDeviceCode(), data.getOriginalProductModel());
				Optional.ofNullable(theoreticalOutput).ifPresent(output -> {
					// 设置理论CT
					Optional.ofNullable(output.getCt()).ifPresent(data::setTheoreticalCt);
					// 设置系统维护的拼板数
					Optional.ofNullable(output.getFlatNumber()).ifPresent(data::setFlatNum);
				});
				return data;
			})
			.collect(Collectors.groupingBy(data -> {
				if (data.getGroupId() == null) {
					return data.getProductModel();
				}
				else {
					return data.getProductModel() + "_" + data.getGroupId();
				}
			}, Collectors.toCollection(ArrayList::new)));

		// 标记瓶颈设备
		markBottleneckDevices(productModelDataMap);

		return productModelDataMap;
	}

	/**
	 * 标记瓶颈设备
	 */
	private void markBottleneckDevices(Map<String, List<ProductionData>> productModelDataMap) {
		productModelDataMap.values().forEach(productDataList -> {
			// 首先尝试根据理论CT找出瓶颈设备
			Optional<ProductionData> theoreticalBottleneck = productDataList.stream()
				.filter(data -> data.getTheoreticalCt() != null)
				.max(Comparator.comparing(ProductionData::getTheoreticalCt));

			// 如果没有找到理论CT的瓶颈设备，则使用实际CT
			if (theoreticalBottleneck.isPresent()) {
				theoreticalBottleneck.get().setBottleneck(true);
			}
			else {
				productDataList.stream()
					.max(Comparator.comparing(ProductionData::getActualCt))
					.ifPresent(data -> data.setBottleneck(true));
			}
		});
	}

	@Override
	public LineChangeoverInfo getLineChangeoverInfo(AnalyzeQuery query, Map<String, Device> primarySmtDevices) {
		// 获取换线信息
		// LineChangeoverInfo calculateLineChangeInfo =
		// LineChangeoversCalculator.getLineChangeoverInfo(primarySmtDevices,
		// query, mongoTemplate);

		// 获取换线记录
		List<LineChangeoverRecord> lineChangeoverInfo = LineChangeoverWithThresholdCalculator
			.getLineChangeoverInfo(primarySmtDevices, query, mongoTemplate);
		// 计算换线信息
		LineChangeoverInfo calculateLineChangeInfo = LineChangeoverWithThresholdCalculator
			.calculateLineChangeInfo(lineChangeoverInfo);
		return calculateLineChangeInfo;
	}

	@Override
	public Double calculateQuality(AnalyzeQuery query, Integer defectCounts,
			Map<String, List<ProductionData>> productData) {
		// 获取每个产品的拼板数，对生产数据处理转成map对象，key为productmodel
		Map<String, Integer> flatNumMap = productData.values()
			.stream()
			.flatMap(List::stream)
			.filter(productionData -> productionData.getFlatNum() != null && productionData.getFlatNum() > 0)
			.collect(Collectors.toMap(ProductionData::getOriginalProductModel, ProductionData::getFlatNum,
					(existing, replacement) -> existing));

		// 获取AOI设备
		List<Device> aoiDevices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.AOI);

		// AOI生产总数
		int totalCount = aoiDevices.stream()
			.map(device -> AoiQualityCalculator.getAoiTotalCount(device, query, mongoTemplate))
			.flatMap(List::stream)
			.mapToInt(group -> calculateGroupTotalCount(group, flatNumMap))
			.sum();

		return totalCount > 0 ? (double) (totalCount - defectCounts) / totalCount : 0.00;
	}

	/**
	 * 计算AOI产品的拼板总数
	 * @param group
	 * @param flatNumMap
	 * @return
	 */
	private int calculateGroupTotalCount(AoiProductionGroup group, Map<String, Integer> flatNumMap) {
		int actualProduction = group.getActualProduction() != null ? group.getActualProduction() : 0;
		// 不需要计算拼板数
		if (!group.getNeedFlat()) {
			return actualProduction;
		}

		// 需要计算拼板数
		Integer flatNum = group.getFlatNum();
		if (flatNum == null) {
			flatNum = OeeUtil.generateCandidateKeys(group.getProductModel())
				.stream()
				.filter(flatNumMap::containsKey)
				.map(flatNumMap::get)
				.findFirst()
				.orElse(1);
		}

		return actualProduction * flatNum;
	}

	@Override
	public List<DeviceCtInfo> getDevicesCt(AnalyzeQuery query) {
		// // 获取线体下的全部设备
		// List<Device> allDevices = deviceService.getAllDevices(query.getCode());
		// // 遍历设备获取生产信息
		// for (Device device : allDevices) {
		// // 获取设备对应的ct
		// Double deviceCt = DeviceCtCalculator.getDeviceCt(device, query, mongoTemplate);

		// }

		return null;
	}

	/**
	 * 收集每个设备及其对应当前产品型号
	 * @param devices
	 * @param query
	 * @return
	 */
	@Override
	public List<DeviceProductModel> collectDeviceProductModels(List<Device> devices, AnalyzeQuery query) {
		return devices.stream().map(device -> {
			String productModel = ProductModelCalculator.getFirstProductModel(device, query, mongoTemplate);
			return new DeviceProductModel(device, productModel);
		}).collect(Collectors.toList());
	}

	/**
	 * 获取不良类型
	 * @param query 查询条件
	 * @param devices 线体下全部AOI设备
	 */
	@Override
	public List<DeviceDefectType> getDefectTypes(AnalyzeQuery query, List<Device> devices) {
		List<DeviceDefectType> list = new ArrayList<>();
		for (Device device : devices) {
			// 获取不良类型信息
			List<DefectTypeInfo> aoiDefectTypes = AoiDefectTypesCalculator.getAoiDefectTypes(device, query,
					mongoTemplate);
			DeviceDefectType deviceDefectType = BuilderUtil.builder(DeviceDefectType::new)
				.with(DeviceDefectType::setDevice, device)
				.with(DeviceDefectType::setDefectTypeInfos, aoiDefectTypes)
				.build();
			list.add(deviceDefectType);
		}
		return list;
	}

	@Override
	public Integer getDefectTotal(List<DeviceDefectType> defectTypes) {
		return defectTypes.stream()
			.flatMap(deviceDefectType -> deviceDefectType.getDefectTypeInfos().stream())
			.mapToInt(e -> Integer.parseInt(e.getCount()))
			.sum();
	}

	@Override
	public Integer calculateDefectCount(AnalyzeQuery query) {
		Integer result = 0;
		// 获取AOI设备
		List<Device> aoiDevices = deviceService.getDevicesByCategory(query.getCode(), DeviceCategory.AOI);

		// 遍历AOI设备计算不良品数
		for (Device device : aoiDevices) {
			Integer aoiDefectCount = AoiDefectCountCalculator.getAoiDefectCount(device, query, mongoTemplate);
			result += aoiDefectCount;
		}
		return result;
	}

}
