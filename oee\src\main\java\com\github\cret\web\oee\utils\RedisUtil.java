package com.github.cret.web.oee.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.util.concurrent.TimeUnit;

@Component
public class RedisUtil {

	private static RedisTemplate<String, Object> redisTemplate;

	private final RedisTemplate<String, Object> redisTemplateBean;

	@Autowired
	public RedisUtil(RedisTemplate<String, Object> redisTemplate) {
		this.redisTemplateBean = redisTemplate;
	}

	@PostConstruct
	public void init() {
		redisTemplate = this.redisTemplateBean;
	}

	/**
	 * 删除 Redis 中的指定键
	 * @param key 要删除的键
	 * @return 是否成功删除（键存在并被删除返回 true，否则返回 false）
	 */
	public static boolean deleteKey(String key) {
		return Boolean.TRUE.equals(redisTemplate.delete(key));
	}

	/**
	 * 设置键值
	 * @param key 键
	 * @param value 值
	 */
	public static void setValue(String key, Object value) {
		redisTemplate.opsForValue().set(key, value);
	}

	/**
	 * 设置键值并设置过期时间
	 * @param key 键
	 * @param value 值
	 * @param expireTime 过期时长
	 * @param expireTimeUnit 过期时长单位
	 */
	public static void setValue(String key, Object value, Integer expireTime, TimeUnit expireTimeUnit) {
		redisTemplate.opsForValue().set(key, value, expireTime, expireTimeUnit);
	}

	/**
	 * 获取键值
	 * @param key 键
	 * @return 值
	 */
	public static Object getValue(String key) {
		return redisTemplate.opsForValue().get(key);
	}

}
