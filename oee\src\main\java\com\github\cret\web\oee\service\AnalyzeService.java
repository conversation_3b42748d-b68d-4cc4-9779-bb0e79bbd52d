package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.domain.analyze.DeviceCtInfo;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.domain.analyze.ProductionLineResult;
import com.github.cret.web.oee.domain.analyze.query.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.result.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.result.DefectTypeResult;
import com.github.cret.web.oee.domain.analyze.result.HourlyOutputList;

/**
 * 分析服务接口
 */
public interface AnalyzeService {

	/**
	 * 获取当前产品名称
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return 当前产品名称
	 */
	public ProductionLineResult getCurrentProduction(AnalyzeQuery query);

	/**
	 * 查询产线一段时间内的线体OEE
	 * @param query 查询参数，包含线体编码、开始时间和结束时间
	 * @return OEE结果，包含稼动率、运行效率、良品率
	 */
	public OeeResult getLineOee(AnalyzeQuery query);

	/**
	 * 获取指定线体下所有设备在指定时间段内的CT时间信息
	 * @param query 查询参数，包含线体编码、开始时间和结束时间
	 * @return 设备CT时间信息列表
	 */
	public List<DeviceCtInfo> getDevicesCt(AnalyzeQuery query);

	/**
	 * 获取线体每小时产出数据
	 * @param query 查询参数，包含线体编码、开始时间和结束时间
	 * @return 每小时产出数据列表
	 */
	public HourlyOutputList getHourlyOutput(AnalyzeQuery query);

	/**
	 * 获取不良类型统计数据
	 * @param query 查询参数，包含线体编码、开始时间和结束时间
	 * @return 不良类型统计列表
	 */
	public DefectTypeResult getDefectTypes(AnalyzeQuery query);

	/**
	 * 获取Top5异常统计数据
	 * @param query 查询参数，包含线体编码、开始时间和结束时间
	 * @return Top5异常统计列表
	 */
	public List<AlarmInfo> getTopAlarms(AnalyzeQuery query);

	/**
	 * 根据车间编码获取每条线体的单月OEE数据
	 * @param query
	 * @return
	 */
	public List<OeeResult> getWorkshopMonthlyOee(String workShopId, AnalyzeQuery query);

}