package com.github.cret.web.oee.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.document.feedback.ResponseConfig;
import com.github.cret.web.oee.domain.query.FeedbackTriggerRecordQuery;
import com.github.cret.web.oee.repository.AnomaliesClassificationRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerRecordRepository;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class FeedbackTriggerRecordServiceImpl implements FeedbackTriggerRecordService {

	private final FeedbackTriggerRecordRepository repository;

	private final AnomaliesClassificationRepository anomaliesClassificationRepository;

	private final FeedbackTriggerSendService feedbackTriggerSendService;

	private final MongoTemplate mongoTemplate;

	public FeedbackTriggerRecordServiceImpl(FeedbackTriggerRecordRepository repository,
			AnomaliesClassificationRepository anomaliesClassificationRepository,
			FeedbackTriggerSendService feedbackTriggerSendService, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.anomaliesClassificationRepository = anomaliesClassificationRepository;
		this.feedbackTriggerSendService = feedbackTriggerSendService;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	@Transactional
	public FeedbackTriggerRecord save(FeedbackTriggerRecord record) {
		// 设置触发时间，如果未设置则使用当前时间
		if (record.getTriggerTime() == null) {
			record.setTriggerTime(new Date());
		}

		// 设置触发人信息
		AuthUser authUser = SecurityUtil.getCurrentUser();

		if (record.getTriggerUserId() == null) {
			record.setTriggerUserId(authUser.id());
		}

		if (record.getTriggerUserName() == null) {
			record.setTriggerUserName(authUser.name());
		}

		// 初始设置异常未关闭
		record.setTriggerClose(false);

		// 先保存触发记录以获取ID
		FeedbackTriggerRecord savedRecord = repository.save(record);

		// 创建发送记录，根据异常编码和线体编码查找异常分类配置，根据异常分类配置生成发送表
		AnomaliesClassification anomaliesClassification = anomaliesClassificationRepository
			.findByLineCodeAndAnomaliesCode(record.getLineCode(), record.getAnomaliesCode())
			.orElse(null);

		if (anomaliesClassification != null && anomaliesClassification.getResponseConfig() != null) {
			// 获取异常分类配置
			List<ResponseConfig> responseConfigs = anomaliesClassification.getResponseConfig();

			// 为每个响应配置创建发送记录
			for (ResponseConfig config : responseConfigs) {
				feedbackTriggerSendService.createFeedbackTriggerSend(savedRecord, config);
			}
		}

		return savedRecord;
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);
	}

	@Override
	public FeedbackTriggerRecord findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerRecord> search(FeedbackTriggerRecordQuery query) {
		Query mongoQuery = new Query();
		List<Criteria> criteria = new ArrayList<>();

		if (StringUtils.hasText(query.getAnomaliesCode())) {
			criteria.add(Criteria.where("anomaliesCode").is(query.getAnomaliesCode()));
		}

		if (StringUtils.hasText(query.getLineCode())) {
			criteria.add(Criteria.where("lineCode").is(query.getLineCode()));
		}

		if (StringUtils.hasText(query.getTriggerId())) {
			criteria.add(Criteria.where("triggerId").is(query.getTriggerId()));
		}

		if (query.getStartTime() != null) {
			criteria.add(Criteria.where("triggerTime").gte(query.getStartTime()));
		}

		if (query.getEndTime() != null) {
			criteria.add(Criteria.where("triggerTime").lte(query.getEndTime()));
		}

		if (!criteria.isEmpty()) {
			mongoQuery.addCriteria(new Criteria().andOperator(criteria.toArray(new Criteria[0])));
		}

		long total = mongoTemplate.count(mongoQuery, FeedbackTriggerRecord.class);

		mongoQuery.with(query.getPageRequest());
		List<FeedbackTriggerRecord> list = mongoTemplate.find(mongoQuery, FeedbackTriggerRecord.class);

		PageList<FeedbackTriggerRecord> result = new PageList<>();
		result.setList(list);
		result.setTotal(total);
		result.setHasNext((long) query.getPageRequest().getOffset() + query.getPageRequest().getPageSize() < total);

		return result;
	}

	@Override
	public FeedbackTriggerRecord update(String id, FeedbackTriggerRecord record) {
		FeedbackTriggerRecord existing = findById(id);
		record.setId(existing.getId());
		return repository.save(record);
	}

	@Override
	public boolean hasOpenExceptions(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		return mongoTemplate.exists(query, FeedbackTriggerRecord.class);
	}

	@Override
	public FeedbackTriggerRecord findLatestOpenException(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		query.with(Sort.by(Sort.Direction.DESC, "triggerTime"));
		query.limit(1);
		return mongoTemplate.findOne(query, FeedbackTriggerRecord.class);
	}

	@Override
	public FeedbackTriggerRecord closeException(String id) {
		return repository.findById(id).map(record -> {
			record.setTriggerClose(true);
			record.setTriggerCloseTime(new Date());
			return repository.save(record);
		}).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	@Transactional
	public int batchCloseException(List<String> ids) {
		if (ids == null || ids.isEmpty()) {
			return 0;
		}

		Date closeTime = new Date();
		int successCount = 0;

		for (String id : ids) {
			try {
				boolean updated = repository.findById(id).map(record -> {
					record.setTriggerClose(true);
					record.setTriggerCloseTime(closeTime);
					repository.save(record);
					return true;
				}).orElse(false);

				if (updated) {
					successCount++;
				}
			}
			catch (Exception e) {
				// 记录错误但继续处理其他记录
				// 可以考虑添加日志记录
			}
		}

		return successCount;
	}

	@Override
	public List<NoticeUser> getNoticeUsers(String id) {
		FeedbackTriggerRecord record = findById(id);
		List<NoticeUser> noticeUsers = record.getNoticeUsers();
		return noticeUsers != null ? noticeUsers : new ArrayList<>();
	}

}
